import React from 'react';
import { 
  ChevronLeft,
  Search,
  Mail
} from 'lucide-react';

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface CreateUserProps {
  setActiveTab: (tab: string) => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

const CreateUser: React.FC<CreateUserProps> = ({ setActiveTab }) => {
  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button onClick={() => setActiveTab('user-list')} className="text-[#2aa45c] hover:text-[#045024]">
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">Invite New User</h2>
        </div>

        <div className="max-w-2xl">
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Search Azure AD Users</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                <input
                  type="text"
                  placeholder="Search by name or email..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                />
              </div>
            </div>

            <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
              <h4 className="font-medium text-[#045024] mb-3">Search Results</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-white rounded border">
                  <div>
                    <div className="font-medium">Alex Thompson</div>
                    <div className="text-sm text-gray-500"><EMAIL></div>
                  </div>
                  <ActionButton size="sm">Select</ActionButton>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded border">
                  <div>
                    <div className="font-medium">Emma Davis</div>
                    <div className="text-sm text-gray-500"><EMAIL></div>
                  </div>
                  <ActionButton size="sm">Select</ActionButton>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent">
                  <option>Payer</option>
                  <option>Finance Officer</option>
                  <option>Senior Finance Officer</option>
                  <option>Senior Finance Officer</option>S

                </select>
              </div>
              {/* <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Department</label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent">
                  <option>Finance</option>
                  <option>IT</option>
                  <option>HR</option>
                  <option>Operations</option>
                </select>
              </div> */}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Welcome Message (Optional)</label>
              <textarea
                rows={4}
                placeholder="Add a personal welcome message for the new user..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
              />
            </div>

            <div className="flex gap-4">
              <ActionButton>
                <Mail size={16} />
                Send Invitation
              </ActionButton>
              <ActionButton variant="secondary" onClick={() => setActiveTab('user-list')}>
                Cancel
              </ActionButton>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateUser;