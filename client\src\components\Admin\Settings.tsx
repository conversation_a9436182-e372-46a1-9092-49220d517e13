import React from 'react';
import { 
  FileText,
  Save,
  Download
} from 'lucide-react';  
  
interface AuditLog {
  id: number;
  user: string;
  action: string;
  details: string;
  timestamp: string;
  ip: string;
}

  interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

  const UserSettings: React.FC = () => {
    const auditLogs: AuditLog[] = [
    { id: 1, user: 'John Doe', action: 'User Login', details: 'Successful login from desktop', timestamp: '2025-07-01 09:30:15', ip: '*************' },
    { id: 2, user: 'Jane Smith', action: 'User Created', details: 'Created new user: Mike Johnson', timestamp: '2025-06-29 14:22:33', ip: '*************' },
    { id: 3, user: 'System', action: 'Email Sent', details: 'Invitation email <NAME_EMAIL>', timestamp: '2025-06-29 14:23:01', ip: 'System' },
    { id: 4, user: 'John Doe', action: 'Role Changed', details: 'Changed Sarah Wilson role from User to Manager', timestamp: '2025-06-28 10:15:22', ip: '*************' },
  ];
   return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-semibold text-[#045024] mb-4">Reports Assignment</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Select User</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent">
                <option>John Doe (Admin)</option>
                <option>Jane Smith (Manager)</option>
                <option>Sarah Wilson (Manager)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Available Reports</label>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {['Payment Summary Report', 'User Activity Report', 'Financial Overview', 'Audit Trail Report', 'System Performance'].map((report) => (
                  <label key={report} className="flex items-center">
                    <input type="checkbox" className="mr-2" />
                    <span className="text-sm">{report}</span>
                  </label>
                ))}
              </div>
            </div>
            <ActionButton>
              <FileText size={16} />
              Assign Reports
            </ActionButton>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-[#045024]">System Audit Logs</h3>
          <ActionButton size="sm" variant="secondary">
            <Download size={16} />
            Export Logs
          </ActionButton>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">User</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Action</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Details</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">IP Address</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Timestamp</th>
              </tr>
            </thead>
            <tbody>
              {auditLogs.map(log => (
                <tr key={log.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4 font-medium">{log.user}</td>
                  <td className="py-3 px-4">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#dddeda] text-[#045024]">
                      {log.action}
                    </span>
                  </td>
                  <td className="py-3 px-4 text-sm text-gray-600">{log.details}</td>
                  <td className="py-3 px-4 text-sm">{log.ip}</td>
                  <td className="py-3 px-4 text-sm text-gray-500">{log.timestamp}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  )};

  export default UserSettings;