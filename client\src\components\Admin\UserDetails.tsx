// import React from 'react';
// import { 
//   ChevronLeft,
//   Save,
//   Mail,
//   UserX,
//   CheckCircle,
//   XCircle,
//   Clock,
//   AlertCircle
// } from 'lucide-react';

// interface User {
//   id: number;
//   name: string;
//   email: string;
//   role: string;
//   status: 'Active' | 'Inactive' | 'Pending';
//   lastLogin: string;
//   invitedDate: string;
// }

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger';
//   size?: 'sm' | 'md';
//   disabled?: boolean;
// }

// interface UserDetailsProps {
//   selectedUser: User | null;
//   setActiveTab: (tab: string) => void;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white"
//   };

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
//     >
//       {children}
//     </button>
//   );
// };

// const UserDetails: React.FC<UserDetailsProps> = ({ selectedUser, setActiveTab }) => {
//   const getStatusIcon = (status: string) => {
//     switch (status) {
//       case 'Active': return <CheckCircle className="text-green-500" size={16} />;
//       case 'Inactive': return <XCircle className="text-red-500" size={16} />;
//       case 'Pending': return <Clock className="text-orange-500" size={16} />;
//       default: return <AlertCircle className="text-gray-500" size={16} />;
//     }
//   };

//   if (!selectedUser) {
//     return (
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <p className="text-gray-500">No user selected</p>
//       </div>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex items-center gap-4 mb-6">
//           <button onClick={() => setActiveTab('user-list')} className="text-[#2aa45c] hover:text-[#045024]">
//             <ChevronLeft size={24} />
//           </button>
//           <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
//         </div>

//         <div className="max-w-4xl">
//           <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//             <div className="lg:col-span-2">
//               <div className="space-y-6">
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
//                     <input
//                       type="text"
//                       defaultValue={selectedUser.name}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                     />
//                   </div>
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
//                     <input
//                       type="email"
//                       defaultValue={selectedUser.email}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                     />
//                   </div>
//                 </div>

//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
//                     <select 
//                       defaultValue={selectedUser.role}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                     >
//                       <option>Payer</option>
//                       <option>Finance Officer</option>
//                       <option>Senior Finance Officer</option>
//                     </select>
//                   </div>
//                 </div>

//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
//                   <div className="flex items-center gap-4">
//                     <label className="flex items-center">
//                       <input type="radio" name="status" defaultChecked={selectedUser.status === 'Active'} className="mr-2" />
//                       Active
//                     </label>
//                     <label className="flex items-center">
//                       <input type="radio" name="status" defaultChecked={selectedUser.status === 'Inactive'} className="mr-2" />
//                       Inactive
//                     </label>
//                   </div>
//                 </div>

//                 <div className="flex gap-4">
//                   <ActionButton>
//                     <Save size={16} />
//                     Save Changes
//                   </ActionButton>
//                   <ActionButton variant="secondary">
//                     <Mail size={16} />
//                     Resend Invitation
//                   </ActionButton>
//                   <ActionButton variant="danger">
//                     <UserX size={16} />
//                     Deactivate User
//                   </ActionButton>
//                 </div>
//               </div>
//             </div>

//             <div>
//               <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
//                 <h4 className="font-medium text-[#045024] mb-3">User Information</h4>
//                 <div className="space-y-3 text-sm">
//                   <div>
//                     <span className="text-gray-600">User ID:</span>
//                     <span className="ml-2 font-medium">{selectedUser.id}</span>
//                   </div>
//                   <div>
//                     <span className="text-gray-600">Invited Date:</span>
//                     <span className="ml-2 font-medium">{selectedUser.invitedDate}</span>
//                   </div>
//                   <div>
//                     <span className="text-gray-600">Last Login:</span>
//                     <span className="ml-2 font-medium">{selectedUser.lastLogin}</span>
//                   </div>
//                   <div className="flex items-center">
//                     <span className="text-gray-600">Status:</span>
//                     <div className="ml-2 flex items-center gap-1">
//                       {getStatusIcon(selectedUser.status)}
//                       <span className="font-medium">{selectedUser.status}</span>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default UserDetails;

// import React, { useState, useEffect } from 'react';
// import { useNavigate, useLocation } from 'react-router-dom';
// import { 
//   ChevronLeft,
//   Save,
//   Mail,
//   UserX,
//   CheckCircle,
//   XCircle,
//   Clock,
//   AlertCircle,
//   Edit,
//   Eye
// } from 'lucide-react';

// interface User {
//   id: number;
//   name: string;
//   email: string;
//   role: string;
//   status: 'Active' | 'Inactive' | 'Pending';
//   lastLogin: string;
//   invitedDate: string;
// }

// interface ActionButtonProps {
//   children: React.ReactNode;
//   onClick?: () => void;
//   variant?: 'primary' | 'secondary' | 'danger';
//   size?: 'sm' | 'md';
//   disabled?: boolean;
// }

// interface UserDetailsProps {
//   selectedUser: User | null;
//   setActiveTab: (tab: string) => void;
// }

// const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
//   const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
//   const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
//   const variantClasses = {
//     primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
//     secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
//     danger: "bg-red-500 hover:bg-red-600 text-white"
//   };

//   return (
//     <button
//       onClick={onClick}
//       disabled={disabled}
//       className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
//     >
//       {children}
//     </button>
//   );
// };

// const UserDetails: React.FC<UserDetailsProps> = ({ selectedUser, setActiveTab }) => {
//   const navigate = useNavigate();
//   const location = useLocation();
//   const [mode, setMode] = useState<'view' | 'edit'>('view');
//   const [formData, setFormData] = useState({
//     firstName: '',
//     lastName: '',
//     email: '',
//     role: '',
//     status: 'Active' as 'Active' | 'Inactive' | 'Pending'
//   });

//   useEffect(() => {
//     // Get mode from navigation state
//     const navMode = location.state?.mode || 'view';
//     setMode(navMode);
    
//     // Initialize form data from selected user
//     if (selectedUser) {
//       const nameParts = selectedUser.name.split(' ');
//       setFormData({
//         firstName: nameParts[0] || '',
//         lastName: nameParts.slice(1).join(' ') || '',
//         email: selectedUser.email,
//         role: selectedUser.role,
//         status: selectedUser.status
//       });
//     }
//   }, [selectedUser, location.state]);

//   const getStatusIcon = (status: string) => {
//     switch (status) {
//       case 'Active': return <CheckCircle className="text-green-500" size={16} />;
//       case 'Inactive': return <XCircle className="text-red-500" size={16} />;
//       case 'Pending': return <Clock className="text-orange-500" size={16} />;
//       default: return <AlertCircle className="text-gray-500" size={16} />;
//     }
//   };

//   const handleBackToList = () => {
//     navigate('/admin-dashboard/user-list');
//   };

//   const handleSaveChanges = () => {
//     // Here you would typically save the changes to your backend
//     console.log('Saving changes:', formData);
//     // After saving, you might want to go back to view mode or show a success message
//     setMode('view');
//   };

//   const handleResendInvitation = () => {
//     // Handle resending invitation
//     console.log('Resending invitation to:', selectedUser?.email);
//   };

//   const handleDeactivateUser = () => {
//     // Handle user deactivation
//     console.log('Deactivating user:', selectedUser?.id);
//     // You might want to show a confirmation dialog here
//     if (window.confirm('Are you sure you want to deactivate this user?')) {
//       setFormData(prev => ({ ...prev, status: 'Inactive' }));
//     }
//   };

//   const handleInputChange = (field: string, value: string) => {
//     setFormData(prev => ({ ...prev, [field]: value }));
//   };

//   if (!selectedUser) {
//     return (
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex items-center gap-4 mb-6">
//           <button onClick={handleBackToList} className="text-[#2aa45c] hover:text-[#045024]">
//             <ChevronLeft size={24} />
//           </button>
//           <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
//         </div>
//         <p className="text-gray-500">No user selected</p>
//       </div>
//     );
//   }

//   return (
//     <div className="space-y-6">
//       <div className="bg-white rounded-lg shadow-md p-6">
//         <div className="flex items-center justify-between mb-6">
//           <div className="flex items-center gap-4">
//             <button onClick={handleBackToList} className="text-[#2aa45c] hover:text-[#045024]">
//               <ChevronLeft size={24} />
//             </button>
//             <h2 className="text-xl font-semibold text-[#045024]">
//               {mode === 'edit' ? 'Edit User' : 'User Details'}
//             </h2>
//           </div>
//           <div className="flex items-center gap-2">
//             {mode === 'view' ? (
//               <ActionButton onClick={() => setMode('edit')} variant="secondary">
//                 <Edit size={16} />
//                 Edit User
//               </ActionButton>
//             ) : (
//               <ActionButton onClick={() => setMode('view')} variant="secondary">
//                 <Eye size={16} />
//                 View Mode
//               </ActionButton>
//             )}
//           </div>
//         </div>

//         <div className="max-w-4xl">
//           <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
//             <div className="lg:col-span-2">
//               <div className="space-y-6">
//                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
//                     {mode === 'edit' ? (
//                       <input
//                         type="text"
//                         value={formData.firstName}
//                         onChange={(e) => handleInputChange('firstName', e.target.value)}
//                         className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                       />
//                     ) : (
//                       <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
//                         {formData.firstName}
//                       </div>
//                     )}
//                   </div>
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
//                     {mode === 'edit' ? (
//                       <input
//                         type="text"
//                         value={formData.lastName}
//                         onChange={(e) => handleInputChange('lastName', e.target.value)}
//                         className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                       />
//                     ) : (
//                       <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
//                         {formData.lastName}
//                       </div>
//                     )}
//                   </div>
//                 </div>

//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
//                   {mode === 'edit' ? (
//                     <input
//                       type="email"
//                       value={formData.email}
//                       onChange={(e) => handleInputChange('email', e.target.value)}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                     />
//                   ) : (
//                     <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
//                       {formData.email}
//                     </div>
//                   )}
//                 </div>

//                 <div>
//                   <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
//                   {mode === 'edit' ? (
//                     <select 
//                       value={formData.role}
//                       onChange={(e) => handleInputChange('role', e.target.value)}
//                       className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
//                     >
//                       <option value="Admin">Admin</option>
//                       <option value="Manager">Manager</option>
//                       <option value="User">User</option>
//                       <option value="Finance Officer">Finance Officer</option>
//                       <option value="Senior Finance Officer">Senior Finance Officer</option>
//                       <option value="Payer">Payer</option>
//                     </select>
//                   ) : (
//                     <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
//                       <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#dddeda] text-[#045024]">
//                         {formData.role}
//                       </span>
//                     </div>
//                   )}
//                 </div>

//                 {mode === 'edit' && (
//                   <div>
//                     <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
//                     <div className="flex items-center gap-4">
//                       <label className="flex items-center">
//                         <input 
//                           type="radio" 
//                           name="status" 
//                           value="Active"
//                           checked={formData.status === 'Active'}
//                           onChange={(e) => handleInputChange('status', e.target.value)}
//                           className="mr-2" 
//                         />
//                         Active
//                       </label>
//                       <label className="flex items-center">
//                         <input 
//                           type="radio" 
//                           name="status" 
//                           value="Inactive"
//                           checked={formData.status === 'Inactive'}
//                           onChange={(e) => handleInputChange('status', e.target.value)}
//                           className="mr-2" 
//                         />
//                         Inactive
//                       </label>
//                       <label className="flex items-center">
//                         <input 
//                           type="radio" 
//                           name="status" 
//                           value="Pending"
//                           checked={formData.status === 'Pending'}
//                           onChange={(e) => handleInputChange('status', e.target.value)}
//                           className="mr-2" 
//                         />
//                         Pending
//                       </label>
//                     </div>
//                   </div>
//                 )}

//                 <div className="flex gap-4">
//                   {mode === 'edit' ? (
//                     <>
//                       <ActionButton onClick={handleSaveChanges}>
//                         <Save size={16} />
//                         Save Changes
//                       </ActionButton>
//                       <ActionButton onClick={handleResendInvitation} variant="secondary">
//                         <Mail size={16} />
//                         Resend Invitation
//                       </ActionButton>
//                       <ActionButton onClick={handleDeactivateUser} variant="danger">
//                         <UserX size={16} />
//                         Deactivate User
//                       </ActionButton>
//                     </>
//                   ) : (
//                     <ActionButton onClick={handleResendInvitation} variant="secondary">
//                       <Mail size={16} />
//                       Resend Invitation
//                     </ActionButton>
//                   )}
//                 </div>
//               </div>
//             </div>

//             <div>
//               <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
//                 <h4 className="font-medium text-[#045024] mb-3">User Information</h4>
//                 <div className="space-y-3 text-sm">
//                   <div>
//                     <span className="text-gray-600">User ID:</span>
//                     <span className="ml-2 font-medium">{selectedUser.id}</span>
//                   </div>
//                   <div>
//                     <span className="text-gray-600">Full Name:</span>
//                     <span className="ml-2 font-medium">{formData.firstName} {formData.lastName}</span>
//                   </div>
//                   <div>
//                     <span className="text-gray-600">Invited Date:</span>
//                     <span className="ml-2 font-medium">{selectedUser.invitedDate}</span>
//                   </div>
//                   <div>
//                     <span className="text-gray-600">Last Login:</span>
//                     <span className="ml-2 font-medium">{selectedUser.lastLogin}</span>
//                   </div>
//                   <div className="flex items-center">
//                     <span className="text-gray-600">Status:</span>
//                     <div className="ml-2 flex items-center gap-1">
//                       {getStatusIcon(formData.status)}
//                       <span className="font-medium">{formData.status}</span>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default UserDetails;


import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useParams } from 'react-router-dom';
import { 
  ChevronLeft,
  Save,
  Mail,
  UserX,
  CheckCircle,
  XCircle,
  Clock,
  AlertCircle,
  Edit,
  Eye
} from 'lucide-react';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  status: 'Active' | 'Inactive' | 'Pending';
  lastLogin: string;
  invitedDate: string;
}

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}

interface UserDetailsProps {
  selectedUser: User | null;
  setActiveTab: (tab: string) => void;
}

const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

const UserDetails: React.FC<UserDetailsProps> = ({ selectedUser }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { userId } = useParams<{ userId: string }>();
  const [mode, setMode] = useState<'view' | 'edit'>('view');
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    role: '',
    status: 'Active' as 'Active' | 'Inactive' | 'Pending'
  });

  // Sample users data - replace with your actual data source or API call
  const sampleUsers: User[] = [
    {
      id: 1,
      name: "John Doe",
      email: "<EMAIL>",
      role: "Admin",
      status: "Active",
      lastLogin: "2024-01-15",
      invitedDate: "2024-01-01"
    },
    {
      id: 2,
      name: "Jane Smith",
      email: "<EMAIL>",
      role: "Finance Officer",
      status: "Active",
      lastLogin: "2024-01-14",
      invitedDate: "2024-01-02"
    },
    {
      id: 3,
      name: "Bob Johnson",
      email: "<EMAIL>",
      role: "Payer",
      status: "Pending",
      lastLogin: "Never",
      invitedDate: "2024-01-10"
    },
    {
      id: 4,
      name: "Alice Brown",
      email: "<EMAIL>",
      role: "Senior Finance Officer",
      status: "Active",
      lastLogin: "2024-01-13",
      invitedDate: "2024-01-03"
    },
    {
      id: 5,
      name: "Charlie Wilson",
      email: "<EMAIL>",
      role: "Payer",
      status: "Inactive",
      lastLogin: "2024-01-05",
      invitedDate: "2024-01-04"
    }
  ];

  // Function to fetch user by ID (replace with actual API call)
  const fetchUserById = async (id: number): Promise<User | null> => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 100));
    return sampleUsers.find(user => user.id === id) || null;
  };

  useEffect(() => {
    const loadUser = async () => {
      setLoading(true);
      
      // First try to use the selectedUser prop
      if (selectedUser && selectedUser.id.toString() === userId) {
        setCurrentUser(selectedUser);
      } else if (userId) {
        // If no selectedUser or ID mismatch, fetch from "API"
        const user = await fetchUserById(parseInt(userId));
        setCurrentUser(user);
      } else {
        setCurrentUser(null);
      }
      
      setLoading(false);
    };

    loadUser();
  }, [userId, selectedUser]);

  useEffect(() => {
    // Get mode from navigation state
    const navMode = location.state?.mode || 'view';
    setMode(navMode);
    
    // Initialize form data from current user
    if (currentUser) {
      const nameParts = currentUser.name.split(' ');
      setFormData({
        firstName: nameParts[0] || '',
        lastName: nameParts.slice(1).join(' ') || '',
        email: currentUser.email,
        role: currentUser.role,
        status: currentUser.status
      });
    }
  }, [currentUser, location.state]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active': return <CheckCircle className="text-green-500" size={16} />;
      case 'Inactive': return <XCircle className="text-red-500" size={16} />;
      case 'Pending': return <Clock className="text-orange-500" size={16} />;
      default: return <AlertCircle className="text-gray-500" size={16} />;
    }
  };

  const handleBackToList = () => {
    navigate('/admin-dashboard/user-list');
  };

  const handleSaveChanges = () => {
    // Here you would typically save the changes to your backend
    console.log('Saving changes:', formData);
    // After saving, you might want to go back to view mode or show a success message
    setMode('view');
  };

  const handleResendInvitation = () => {
    // Handle resending invitation
    console.log('Resending invitation to:', currentUser?.email);
  };

  const handleDeactivateUser = () => {
    // Handle user deactivation
    console.log('Deactivating user:', currentUser?.id);
    // You might want to show a confirmation dialog here
    if (window.confirm('Are you sure you want to deactivate this user?')) {
      setFormData(prev => ({ ...prev, status: 'Inactive' }));
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button onClick={handleBackToList} className="text-[#2aa45c] hover:text-[#045024]">
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
        </div>
        <div className="flex items-center justify-center py-8">
          <div className="text-gray-500">Loading user details...</div>
        </div>
      </div>
    );
  }

  if (!currentUser) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button onClick={handleBackToList} className="text-[#2aa45c] hover:text-[#045024]">
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">User Details</h2>
        </div>
        <div className="text-center py-8">
          <p className="text-gray-500 mb-4">User not found</p>
          <ActionButton onClick={handleBackToList} variant="secondary">
            Back to User List
          </ActionButton>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <button onClick={handleBackToList} className="text-[#2aa45c] hover:text-[#045024]">
              <ChevronLeft size={24} />
            </button>
            <h2 className="text-xl font-semibold text-[#045024]">
              {mode === 'edit' ? 'Edit User' : 'User Details'}
            </h2>
          </div>
          <div className="flex items-center gap-2">
            {mode === 'view' ? (
              <ActionButton onClick={() => setMode('edit')} variant="secondary">
                <Edit size={16} />
                Edit User
              </ActionButton>
            ) : (
              <ActionButton onClick={() => setMode('view')} variant="secondary">
                <Eye size={16} />
                View Mode
              </ActionButton>
            )}
          </div>
        </div>

        <div className="max-w-4xl">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2">
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    {mode === 'edit' ? (
                      <input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) => handleInputChange('firstName', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                      />
                    ) : (
                      <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                        {formData.firstName}
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    {mode === 'edit' ? (
                      <input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) => handleInputChange('lastName', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                      />
                    ) : (
                      <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                        {formData.lastName}
                      </div>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  {mode === 'edit' ? (
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    />
                  ) : (
                    <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                      {formData.email}
                    </div>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                  {mode === 'edit' ? (
                    <select 
                      value={formData.role}
                      onChange={(e) => handleInputChange('role', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                    >
                      <option value="Admin">Admin</option>
                      <option value="Manager">Manager</option>
                      <option value="User">User</option>
                      <option value="Finance Officer">Finance Officer</option>
                      <option value="Senior Finance Officer">Senior Finance Officer</option>
                      <option value="Payer">Payer</option>
                    </select>
                  ) : (
                    <div className="w-full px-3 py-2 bg-gray-50 rounded-lg text-gray-700">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-[#dddeda] text-[#045024]">
                        {formData.role}
                      </span>
                    </div>
                  )}
                </div>

                {mode === 'edit' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Status</label>
                    <div className="flex items-center gap-4">
                      <label className="flex items-center">
                        <input 
                          type="radio" 
                          name="status" 
                          value="Active"
                          checked={formData.status === 'Active'}
                          onChange={(e) => handleInputChange('status', e.target.value)}
                          className="mr-2" 
                        />
                        Active
                      </label>
                      <label className="flex items-center">
                        <input 
                          type="radio" 
                          name="status" 
                          value="Inactive"
                          checked={formData.status === 'Inactive'}
                          onChange={(e) => handleInputChange('status', e.target.value)}
                          className="mr-2" 
                        />
                        Inactive
                      </label>
                      <label className="flex items-center">
                        <input 
                          type="radio" 
                          name="status" 
                          value="Pending"
                          checked={formData.status === 'Pending'}
                          onChange={(e) => handleInputChange('status', e.target.value)}
                          className="mr-2" 
                        />
                        Pending
                      </label>
                    </div>
                  </div>
                )}

                <div className="flex gap-4">
                  {mode === 'edit' ? (
                    <>
                      <ActionButton onClick={handleSaveChanges}>
                        <Save size={16} />
                        Save Changes
                      </ActionButton>
                      <ActionButton onClick={handleResendInvitation} variant="secondary">
                        <Mail size={16} />
                        Resend Invitation
                      </ActionButton>
                      <ActionButton onClick={handleDeactivateUser} variant="danger">
                        <UserX size={16} />
                        Deactivate User
                      </ActionButton>
                    </>
                  ) : (
                    <ActionButton onClick={handleResendInvitation} variant="secondary">
                      <Mail size={16} />
                      Resend Invitation
                    </ActionButton>
                  )}
                </div>
              </div>
            </div>

            <div>
              <div className="bg-[#dddeda] bg-opacity-30 rounded-lg p-4">
                <h4 className="font-medium text-[#045024] mb-3">User Information</h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-600">User ID:</span>
                    <span className="ml-2 font-medium">{currentUser.id}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Full Name:</span>
                    <span className="ml-2 font-medium">{formData.firstName} {formData.lastName}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Invited Date:</span>
                    <span className="ml-2 font-medium">{currentUser.invitedDate}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Last Login:</span>
                    <span className="ml-2 font-medium">{currentUser.lastLogin}</span>
                  </div>
                  <div className="flex items-center">
                    <span className="text-gray-600">Status:</span>
                    <div className="ml-2 flex items-center gap-1">
                      {getStatusIcon(formData.status)}
                      <span className="font-medium">{formData.status}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserDetails;