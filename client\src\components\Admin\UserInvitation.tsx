import { 
//   Plus, 
  Mail,
  Clock,
  Trash2,
} from 'lucide-react';

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  size?: 'sm' | 'md';
  disabled?: boolean;
}


const ActionButton: React.FC<ActionButtonProps> = ({ children, onClick, variant = "primary", size = "md", disabled = false }) => {
  const baseClasses = "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white"
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
    >
      {children}
    </button>
  );
};

const UserInvitations = () => (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-[#045024]">Invitation Status</h2>
          {/* <ActionButton onClick={() => setActiveTab('create-user')}>
            <Plus size={16} />
            Send New Invitation
          </ActionButton> */}
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Email</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Role</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Invited Date</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Status</th>
                <th className="text-left py-3 px-4 font-semibold text-[#045024]">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100">
                <td className="py-3 px-4"><EMAIL></td>
                <td className="py-3 px-4">Payer</td>
                <td className="py-3 px-4">2025-06-29</td>
                <td className="py-3 px-4">
                  <div className="flex items-center gap-2">
                    <Clock className="text-orange-500" size={16} />
                    <span className="text-sm">Pending</span>
                  </div>
                </td>
                <td className="py-3 px-4">
                  <div className="flex items-center gap-2">
                    <ActionButton size="sm">
                      <Mail size={14} />
                      Resend
                    </ActionButton>
                    <ActionButton size="sm" variant="danger">
                      <Trash2 size={14} />
                      Cancel
                    </ActionButton>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

export default UserInvitations;