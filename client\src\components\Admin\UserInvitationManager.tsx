import React, { useState, useEffect } from "react";
import { ChevronLeft, Mail, Loader2, Clock, Trash2 } from "lucide-react";
import { API_BASE_URL } from "../../config/api";

interface ActionButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: "primary" | "secondary" | "danger";
  size?: "sm" | "md";
  disabled?: boolean;
}

interface CreateUserProps {
  setActiveTab?: (tab: string) => void;
  onInvitationSent: (invitation: Invitation) => void;
}

interface UserInvitationsProps {
  invitations: Invitation[];
  onInvitationUpdate: (updatedInvitations: Invitation[]) => void;
}

interface InvitationData {
  email: string;
  role: string;
  organizationId: string;
  authType: number;
}

interface Invitation {
  id: string;
  email: string;
  role: string;
  organizationId: string;
  organizationName: string | null;
  invitedDate: string;
  status: string;
  expiryDate: string;
  acceptedDate: string | null;
  invitedBy: string | null;
  invitedByName: string | null;
}

const ActionButton: React.FC<ActionButtonProps> = ({
  children,
  onClick,
  variant = "primary",
  size = "md",
  disabled = false,
}) => {
  const baseClasses =
    "inline-flex items-center gap-2 font-medium rounded-lg transition-all";
  const sizeClasses = size === "sm" ? "px-3 py-1.5 text-sm" : "px-4 py-2";
  const variantClasses = {
    primary: "bg-[#2aa45c] hover:bg-[#045024] text-white",
    secondary: "bg-[#dddeda] hover:bg-gray-300 text-[#045024]",
    danger: "bg-red-500 hover:bg-red-600 text-white",
  };

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      className={`${baseClasses} ${sizeClasses} ${variantClasses[variant]} ${
        disabled ? "opacity-50 cursor-not-allowed" : ""
      }`}
    >
      {children}
    </button>
  );
};

const CreateUser: React.FC<CreateUserProps> = ({
  setActiveTab,
  onInvitationSent,
}) => {
  const [email, setEmail] = useState("");
  const [role, setRole] = useState("admin");
  const [organizationId, setOrganizationId] = useState("");
  const [authType, setAuthType] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{
    type: "success" | "error";
    text: string;
  } | null>(null);

  const sendInvitation = async () => {
    if (!email.trim()) {
      setMessage({ type: "error", text: "Email is required" });
      return;
    }

    if (!organizationId.trim()) {
      setMessage({ type: "error", text: "Organization ID is required" });
      return;
    }

    setIsLoading(true);
    setMessage(null);

    try {
      const token = localStorage.getItem("auth_token");

      const invitationData: InvitationData = {
        email: email.trim(),
        role: role,
        organizationId: organizationId.trim(),
        authType: authType,
      };

      const response = await fetch(`${API_BASE_URL}/user-invitations/invite`, {
        method: "POST",
        headers: {
          accept: "*/*",
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(invitationData),
      });

      if (response.ok) {
        const result = await response.json();

        // Create a new invitation object to add to the list
        const newInvitation: Invitation = {
          id: result.id || `temp-${Date.now()}`, // Use response ID or temporary ID
          email: email.trim(),
          role: role,
          organizationId: organizationId.trim(),
          organizationName: result.organizationName || null,
          invitedDate: new Date().toISOString(),
          status: "pending",
          expiryDate:
            result.expiryDate ||
            new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          acceptedDate: null,
          invitedBy: result.invitedBy || null,
          invitedByName: result.invitedByName || null,
        };

        // Notify parent component about the new invitation
        onInvitationSent(newInvitation);

        setMessage({
          type: "success",
          text: `Invitation sent successfully to ${email}!`,
        });

        // Reset form after successful invitation
        setTimeout(() => {
          setEmail("");
          setRole("admin");
          setOrganizationId("");
          setAuthType(1);
          setMessage(null);
        }, 2000);
      } else {
        const errorData = await response.json().catch(() => ({}));
        setMessage({
          type: "error",
          text:
            errorData.message ||
            `Failed to send invitation. Status: ${response.status}`,
        });
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      setMessage({
        type: "error",
        text: "Network error. Please check your connection and try again.",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center gap-4 mb-6">
          <button
            onClick={() => setActiveTab("user-list")}
            className="text-[#2aa45c] hover:text-[#045024]"
          >
            <ChevronLeft size={24} />
          </button>
          <h2 className="text-xl font-semibold text-[#045024]">
            Invite New User
          </h2>
        </div>

        <div className="max-w-2xl">
          <div className="space-y-6">
            {/* Message Display */}
            {message && (
              <div
                className={`p-3 rounded-lg ${
                  message.type === "success"
                    ? "bg-green-100 text-green-800 border border-green-300"
                    : "bg-red-100 text-red-800 border border-red-300"
                }`}
              >
                {message.text}
              </div>
            )}

            {/* Email Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter user's email"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                required
              />
            </div>

            {/* Role Selection and Auth Type */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Role <span className="text-red-500">*</span>
                </label>
                <select
                  value={role}
                  onChange={(e) => setRole(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  required
                >
                  <option value="admin">Admin</option>
                  <option value="payer">Payer</option>
                  <option value="finance_officer">Finance Officer</option>
                  <option value="senior_finance_officer">
                    Senior Finance Officer
                  </option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Authentication Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={authType}
                  onChange={(e) => setAuthType(Number(e.target.value))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  required
                >
                  <option value={0}>Microsoft</option>
                  <option value={1}>Local</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Organization ID <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={organizationId}
                  onChange={(e) => setOrganizationId(e.target.value)}
                  placeholder="e.g., org-001"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#2aa45c] focus:border-transparent"
                  required
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-4">
              <ActionButton
                onClick={sendInvitation}
                disabled={!email.trim() || !organizationId.trim() || isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 size={16} className="animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail size={16} />
                    Send Invitation
                  </>
                )}
              </ActionButton>
              {setActiveTab && (
                <ActionButton
                  variant="secondary"
                  onClick={() => setActiveTab("user-list")}
                >
                  Cancel
                </ActionButton>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const UserInvitations: React.FC<UserInvitationsProps> = ({
  invitations,
  onInvitationUpdate,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resendingId, setResendingId] = useState<string | null>(null);
  const [deletingId, setDeletingId] = useState<string | null>(null);

  const handleResendInvitation = async (id: string) => {
    try {
      setResendingId(id);
      setError(null);
      const token = localStorage.getItem("auth_token");
      const response = await fetch(
        `${API_BASE_URL}/user-invitations/${id}/resend`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to resend invitation");
      }

      // Update the invitation's invited date
      const updatedInvitations = invitations.map((inv) =>
        inv.id === id ? { ...inv, invitedDate: new Date().toISOString() } : inv
      );
      onInvitationUpdate(updatedInvitations);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to resend invitation"
      );
    } finally {
      setResendingId(null);
    }
  };

  const handleDeleteInvitation = async (id: string) => {
    try {
      setDeletingId(id);
      setError(null);
      const token = localStorage.getItem("auth_token");
      const response = await fetch(`${API_BASE_URL}/user-invitations/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to delete invitation");
      }

      // Remove the deleted invitation from the list
      const updatedInvitations = invitations.filter((inv) => inv.id !== id);
      onInvitationUpdate(updatedInvitations);
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to delete invitation"
      );
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const formatRole = (role: string) => {
    const roleMap: Record<string, string> = {
      admin: "Admin",
      payer: "Payer",
      finance_officer: "Finance Officer",
      senior_finance_officer: "Senior Finance Officer",
    };
    return roleMap[role] || role;
  };

  if (error) {
    return (
      <div className="bg-red-100 text-red-800 p-4 rounded-lg mb-4">
        Error: {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-[#045024]">
            Invitation Status ({invitations.length})
          </h2>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-32">
            <Loader2 className="animate-spin text-[#2aa45c]" size={32} />
          </div>
        ) : invitations.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            No pending invitations found
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Email
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Role
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Organization
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Invited Date
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Status
                  </th>
                  <th className="text-left py-3 px-4 font-semibold text-[#045024]">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {invitations.map((invitation) => (
                  <tr key={invitation.id} className="border-b border-gray-100">
                    <td className="py-3 px-4">{invitation.email}</td>
                    <td className="py-3 px-4">{formatRole(invitation.role)}</td>
                    <td className="py-3 px-4">{invitation.organizationId}</td>
                    <td className="py-3 px-4">
                      {formatDate(invitation.invitedDate)}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <Clock className="text-orange-500" size={16} />
                        <span className="text-sm capitalize">
                          {invitation.status}
                        </span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <ActionButton
                          size="sm"
                          onClick={() => handleResendInvitation(invitation.id)}
                          disabled={resendingId === invitation.id}
                        >
                          {resendingId === invitation.id ? (
                            <Loader2 className="animate-spin" size={14} />
                          ) : (
                            <Mail size={14} />
                          )}
                          Resend
                        </ActionButton>
                        <ActionButton
                          size="sm"
                          variant="danger"
                          onClick={() => handleDeleteInvitation(invitation.id)}
                          disabled={deletingId === invitation.id}
                        >
                          {deletingId === invitation.id ? (
                            <Loader2 className="animate-spin" size={14} />
                          ) : (
                            <Trash2 size={14} />
                          )}
                          Cancel
                        </ActionButton>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

// Main component that combines both CreateUser and UserInvitations
const UserInvitationManager: React.FC<{
  setActiveTab?: (tab: string) => void;
}> = ({ setActiveTab }) => {
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch initial invitations
  useEffect(() => {
    const fetchPendingInvitations = async () => {
      try {
        const token = localStorage.getItem("auth_token");
        const response = await fetch(
          `${API_BASE_URL}/user-invitations/pending`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch pending invitations");
        }

        const data = await response.json();
        setInvitations(data);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
      } finally {
        setLoading(false);
      }
    };

    fetchPendingInvitations();
  }, []);

  const handleInvitationSent = (newInvitation: Invitation) => {
    setInvitations((prev) => [newInvitation, ...prev]);
  };

  const handleInvitationUpdate = (updatedInvitations: Invitation[]) => {
    setInvitations(updatedInvitations);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="animate-spin text-[#2aa45c]" size={32} />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-100 text-red-800 p-4 rounded-lg">
        Error loading invitations: {error}
      </div>
    );
  }

  return (
    <div>
      <CreateUser
        setActiveTab={setActiveTab}
        onInvitationSent={handleInvitationSent}
      />
      <UserInvitations
        invitations={invitations}
        onInvitationUpdate={handleInvitationUpdate}
      />
    </div>
  );
};

export default UserInvitationManager;
