// src/components/ProtectedRoute.tsx
import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../contexts/AuthContext";

import type { UserRole } from "../types";

interface ProtectedRouteProps {
  children: React.ReactNode;
  allowedRoles?: UserRole[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  allowedRoles,
}) => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  // Check if user has required role
  if (allowedRoles && user && !allowedRoles.includes(user.role as UserRole)) {
    // Redirect to appropriate dashboard based on role
    switch (user.role) {
      case "JTB_ADMIN":
        return <Navigate to="/admin-dashboard" replace />;
      case "FINANCE_OFFICER":
        return <Navigate to="/finance-officer-dashboard" replace />;
      case "SENIOR_FINANCE_OFFICER":
        return <Navigate to="/senior-finance-officer-dashboard" replace />;
      case "PAYER":
        return <Navigate to="/payerdashboard" replace />;
      default:
        return <Navigate to="/" replace />;
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
