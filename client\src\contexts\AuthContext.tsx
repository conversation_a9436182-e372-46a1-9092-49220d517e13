// AuthContext.tsx - Updated with better state management
import React, { createContext, useState, useEffect, useContext } from "react";
import type { ReactNode } from "react";

interface User {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  organizationId?: string | null;
  isActive: boolean;
  createdAt: string;
  lastLogin?: string | null;
}

interface LoginRequest {
  authMethod: "local" | "microsoft";
  credentials?: {
    email: string;
    password: string;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (request: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  hasRole: (role: string) => boolean;
  permissions: any;
}

export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true); // Start with true for initialization

  console.log("🏗️ AuthProvider render:", {
    isAuthenticated,
    user: user?.email,
  });

  const login = async (request: LoginRequest): Promise<void> => {
    console.log("🚀 LOGIN FUNCTION CALLED");
    console.log("📨 Request:", request);

    setIsLoading(true);
    console.log("⏳ Loading set to true");

    try {
      let apiUrl: string;
      let requestBody: any;

      if (request.authMethod === "local") {
        apiUrl = "http://localhost:5248/api/auth/login";
        requestBody = {
          email: request.credentials?.email,
          password: request.credentials?.password,
        };
        console.log("🏠 Using local auth endpoint:", apiUrl);
      } else {
        apiUrl = "http://localhost:5248/api/auth/exchange";
        requestBody = {};
        console.log("🏢 Using Microsoft auth endpoint:", apiUrl);
      }

      console.log("📡 Making API request...");
      console.log("📦 Body:", requestBody);

      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      console.log("📨 Response received");
      console.log("✅ Status:", response.status);
      console.log("🏷️ Status Text:", response.statusText);

      if (!response.ok) {
        console.log("❌ Response not OK");
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

        try {
          const errorData = await response.json();
          console.log("📄 Error data:", errorData);
          errorMessage = errorData.message || errorData.title || errorMessage;
        } catch (parseError) {
          console.log("⚠️ Could not parse error response as JSON");
        }

        throw new Error(errorMessage);
      }

      console.log("✅ Response OK, parsing data...");
      const data = await response.json();
      console.log("📊 Response data:", data);

      if (!data) {
        throw new Error("No data received from server");
      }

      if (!data.token) {
        console.log("❌ No token in response");
        console.log("🔍 Available keys:", Object.keys(data));
        throw new Error("No authentication token received");
      }

      if (!data.user) {
        console.log("❌ No user in response");
        console.log("🔍 Available keys:", Object.keys(data));
        throw new Error("No user data received");
      }

      console.log("✅ Response validation passed");
      console.log("🎫 Token:", data.token.substring(0, 20) + "...");
      console.log("👤 User:", data.user);

      // Store in localStorage first
      console.log("💾 Storing in localStorage...");
      localStorage.setItem("auth_token", data.token);
      localStorage.setItem("user_data", JSON.stringify(data.user));

      // Then update state
      console.log("💾 Updating auth state...");
      setUser(data.user);
      setIsAuthenticated(true);

      console.log("✅ Login process completed successfully");
      console.log("🔄 Final auth state:", {
        isAuthenticated: true,
        user: data.user.email,
        role: data.user.role,
      });
    } catch (error) {
      console.error("❌ LOGIN ERROR:", error);

      // Clear any partial state
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem("auth_token");
      localStorage.removeItem("user_data");

      throw error;
    } finally {
      console.log("⏳ Setting loading to false");
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    console.log("🚪 Logout called");

    try {
      const token = localStorage.getItem("auth_token");
      if (token) {
        await fetch("http://localhost:5248/api/auth/logout", {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        });
      }
    } catch (error) {
      console.error("Logout API error:", error);
    }

    // Always clear local state
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user_data");
  };

  // Initialize auth state from localStorage on app start
  useEffect(() => {
    const initializeAuth = async () => {
      console.log("🔄 Initializing auth state...");

      try {
        const storedToken = localStorage.getItem("auth_token");
        const storedUser = localStorage.getItem("user_data");

        if (storedToken && storedUser) {
          const userData = JSON.parse(storedUser);

          // Verify the token is still valid (optional)
          try {
            const response = await fetch(
              "http://localhost:5248/api/auth/verify",
              {
                headers: {
                  Authorization: `Bearer ${storedToken}`,
                  "Content-Type": "application/json",
                },
              }
            );

            if (response.ok) {
              setUser(userData);
              setIsAuthenticated(true);
              console.log(
                "🔄 Auth restored from localStorage:",
                userData.email
              );
            } else {
              console.log("⚠️ Token verification failed, clearing storage");
              localStorage.removeItem("auth_token");
              localStorage.removeItem("user_data");
            }
          } catch (verifyError) {
            // If verification endpoint doesn't exist, just restore from localStorage
            console.log(
              "⚠️ Token verification endpoint not available, restoring anyway"
            );
            setUser(userData);
            setIsAuthenticated(true);
            console.log("🔄 Auth restored from localStorage:", userData.email);
          }
        } else {
          console.log("ℹ️ No stored auth data found");
        }
      } catch (error) {
        console.error("⚠️ Error restoring auth:", error);
        localStorage.removeItem("auth_token");
        localStorage.removeItem("user_data");
      } finally {
        setIsLoading(false); // Always set loading to false after initialization
      }
    };

    initializeAuth();
  }, []);

  const hasRole = (role: string): boolean => {
    return user?.role === role;
  };

  const permissions = {
    canManagePayments:
      user?.role === "JTB_ADMIN" || user?.role === "SENIOR_FINANCE_OFFICER",
    canApprovePayments: user?.role === "SENIOR_FINANCE_OFFICER",
    canAcknowledgePayments: user?.role === "FINANCE_OFFICER",
    canManageUsers: user?.role === "JTB_ADMIN",
    canManageOrganizations: user?.role === "JTB_ADMIN",
    canViewReports: user?.role !== "PAYER",
    canManageCompliance: user?.role === "JTB_ADMIN",
  };

  // Log state changes
  useEffect(() => {
    console.log("🔄 Auth state changed:", {
      isAuthenticated,
      user: user?.email,
      role: user?.role,
      isLoading,
    });
  }, [isAuthenticated, user, isLoading]);

  const contextValue: AuthContextType = {
    user,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasRole,
    permissions,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// // AuthContext.tsx - Fixed to work with your actual API
// import React, { createContext, useState, useEffect, useContext } from "react";
// import type { ReactNode } from "react";

// interface User {
//   id: string;
//   firstName: string;
//   lastName: string;
//   email: string;
//   role: string;
//   organizationId?: string | null;
//   isActive: boolean;
//   createdAt: string;
//   lastLogin?: string | null;
// }

// interface LoginRequest {
//   authMethod: "local" | "microsoft";
//   credentials?: {
//     email: string;
//     password: string;
//   };
// }

// interface AuthContextType {
//   user: User | null;
//   isAuthenticated: boolean;
//   isLoading: boolean;
//   login: (request: LoginRequest) => Promise<void>;
//   logout: () => Promise<void>;
//   hasRole: (role: string) => boolean;
//   permissions: any;
// }

// export const AuthContext = createContext<AuthContextType | undefined>(
//   undefined
// );

// export const useAuth = (): AuthContextType => {
//   const context = useContext(AuthContext);
//   if (context === undefined) {
//     throw new Error("useAuth must be used within an AuthProvider");
//   }
//   return context;
// };

// interface AuthProviderProps {
//   children: ReactNode;
// }

// export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
//   const [user, setUser] = useState<User | null>(null);
//   const [isAuthenticated, setIsAuthenticated] = useState(false);
//   const [isLoading, setIsLoading] = useState(false);

//   console.log("🏗️ AuthProvider render:", {
//     isAuthenticated,
//     user: user?.email,
//   });

//   const login = async (request: LoginRequest): Promise<void> => {
//     console.log("🚀 LOGIN FUNCTION CALLED");
//     console.log("📨 Request:", request);

//     setIsLoading(true);
//     console.log("⏳ Loading set to true");

//     try {
//       let apiUrl: string;
//       let requestBody: any;

//       if (request.authMethod === "local") {
//         // Use your actual API endpoint
//         apiUrl = "http://localhost:5248/api/auth/login";
//         requestBody = {
//           email: request.credentials?.email,
//           password: request.credentials?.password,
//         };
//         console.log("🏠 Using local auth endpoint:", apiUrl);
//       } else {
//         // Microsoft endpoint (if it exists)
//         apiUrl = "http://localhost:5248/api/auth/microsoft";
//         requestBody = {};
//         console.log("🏢 Using Microsoft auth endpoint:", apiUrl);
//       }

//       console.log("📡 Making API request...");
//       console.log("📦 Body:", requestBody);

//       const response = await fetch(apiUrl, {
//         method: "POST",
//         headers: {
//           "Content-Type": "application/json",
//           Accept: "application/json",
//         },
//         body: JSON.stringify(requestBody),
//       });

//       console.log("📨 Response received");
//       console.log("✅ Status:", response.status);
//       console.log("🏷️ Status Text:", response.statusText);

//       if (!response.ok) {
//         console.log("❌ Response not OK");
//         let errorMessage = `HTTP ${response.status}: ${response.statusText}`;

//         try {
//           const errorData = await response.json();
//           console.log("📄 Error data:", errorData);
//           errorMessage = errorData.message || errorData.title || errorMessage;
//         } catch (parseError) {
//           console.log("⚠️ Could not parse error response as JSON");
//         }

//         throw new Error(errorMessage);
//       }

//       console.log("✅ Response OK, parsing data...");
//       const data = await response.json();
//       console.log("📊 Response data:", data);

//       // Validate response structure matches your API
//       if (!data) {
//         throw new Error("No data received from server");
//       }

//       if (!data.token) {
//         console.log("❌ No token in response");
//         console.log("🔍 Available keys:", Object.keys(data));
//         throw new Error("No authentication token received");
//       }

//       if (!data.user) {
//         console.log("❌ No user in response");
//         console.log("🔍 Available keys:", Object.keys(data));
//         throw new Error("No user data received");
//       }

//       console.log("✅ Response validation passed");
//       console.log("🎫 Token:", data.token.substring(0, 20) + "...");
//       console.log("👤 User:", data.user);

//       // Update state - CRITICAL: Make sure this happens
//       console.log("💾 Updating auth state...");
//       setUser(data.user);
//       setIsAuthenticated(true);

//       // Store in localStorage
//       console.log("💾 Storing in localStorage...");
//       localStorage.setItem("auth_token", data.token);
//       localStorage.setItem("user_data", JSON.stringify(data.user));

//       console.log("✅ Login process completed successfully");
//       console.log("🔄 Final auth state:", {
//         isAuthenticated: true,
//         user: data.user.email,
//         role: data.user.role,
//       });
//     } catch (error) {
//       console.error("❌ LOGIN ERROR:", error);

//       // Clear any partial state
//       setUser(null);
//       setIsAuthenticated(false);
//       localStorage.removeItem("auth_token");
//       localStorage.removeItem("user_data");

//       throw error;
//     } finally {
//       console.log("⏳ Setting loading to false");
//       setIsLoading(false);
//     }
//   };

//   const logout = async (): Promise<void> => {
//     console.log("🚪 Logout called");

//     try {
//       // Call your logout endpoint if it exists
//       const token = localStorage.getItem("auth_token");
//       if (token) {
//         await fetch("http://localhost:5248/api/auth/logout", {
//           method: "POST",
//           headers: {
//             Authorization: `Bearer ${token}`,
//             "Content-Type": "application/json",
//           },
//         });
//       }
//     } catch (error) {
//       console.error("Logout API error:", error);
//       // Continue with local logout even if API fails
//     }

//     // Always clear local state
//     setUser(null);
//     setIsAuthenticated(false);
//     localStorage.removeItem("auth_token");
//     localStorage.removeItem("user_data");
//   };

//   // Initialize auth state from localStorage on app start
//   useEffect(() => {
//     const initializeAuth = async () => {
//       const storedToken = localStorage.getItem("auth_token");
//       const storedUser = localStorage.getItem("user_data");

//       if (storedToken && storedUser) {
//         try {
//           const userData = JSON.parse(storedUser);

//           // Optional: Verify token is still valid with your API
//           // const response = await fetch('http://localhost:5248/api/auth/verify', {
//           //   headers: { 'Authorization': `Bearer ${storedToken}` },
//           // });
//           //
//           // if (response.ok) {
//           setUser(userData);
//           setIsAuthenticated(true);
//           console.log("🔄 Auth restored from localStorage:", userData.email);
//           // } else {
//           //   localStorage.removeItem('auth_token');
//           //   localStorage.removeItem('user_data');
//           // }
//         } catch (error) {
//           console.error("⚠️ Error restoring auth:", error);
//           localStorage.removeItem("auth_token");
//           localStorage.removeItem("user_data");
//         }
//       }
//     };

//     initializeAuth();
//   }, []);

//   const hasRole = (role: string): boolean => {
//     return user?.role === role;
//   };

//   const permissions = {
//     canManagePayments:
//       user?.role === "JTB_ADMIN" || user?.role === "SENIOR_FINANCE_OFFICER",
//     canApprovePayments: user?.role === "SENIOR_FINANCE_OFFICER",
//     canAcknowledgePayments: user?.role === "FINANCE_OFFICER",
//     canManageUsers: user?.role === "JTB_ADMIN",
//     canManageOrganizations: user?.role === "JTB_ADMIN",
//     canViewReports: user?.role !== "PAYER",
//     canManageCompliance: user?.role === "JTB_ADMIN",
//   };

//   // Log state changes
//   useEffect(() => {
//     console.log("🔄 Auth state changed:", {
//       isAuthenticated,
//       user: user?.email,
//       role: user?.role,
//       isLoading,
//     });
//   }, [isAuthenticated, user, isLoading]);

//   const contextValue: AuthContextType = {
//     user,
//     isAuthenticated,
//     isLoading,
//     login,
//     logout,
//     hasRole,
//     permissions,
//   };

//   return (
//     <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
//   );
// };

// import React, { useEffect, useState, useContext, type ReactNode } from "react";
// import type {
//   User,
//   AuthContextType,
//   UserRole,
//   Permissions,
//   LogoutOptions,
//   AuthError,
//   LoginOptions,
// } from "../types/auth";
// import {
//   signIn,
//   signOut,
//   isLoggedIn,
//   refreshAccessToken,
//   clearTokenRefreshTimer,
// } from "../services/msalAuthService";
// import { authService } from "../services/authService";
// import { msalInstance } from "../auth/msalConfig";
// import {
//   AuthContext,
//   AUTH_TYPE_KEY,
//   LOCAL_TOKEN_KEY,
// } from "./AuthContext.context";

// // Permission mapping based on roles
// const getPermissionsForRole = (role: UserRole): Permissions => {
//   switch (role) {
//     case "JTB_ADMIN":
//       return {
//         canManageUsers: true,
//         canManageOrganizations: true,
//         canManagePayments: true,
//         canAcknowledgePayments: true,
//         canApprovePayments: true,
//         canViewReports: true,
//         canManageCompliance: true,
//       };
//     case "SENIOR_FINANCE_OFFICER":
//       return {
//         canManageUsers: false,
//         canManageOrganizations: false,
//         canManagePayments: true,
//         canAcknowledgePayments: true,
//         canApprovePayments: true,
//         canViewReports: true,
//         canManageCompliance: true,
//       };
//     case "FINANCE_OFFICER":
//       return {
//         canManageUsers: false,
//         canManageOrganizations: false,
//         canManagePayments: true,
//         canAcknowledgePayments: true,
//         canApprovePayments: false,
//         canViewReports: true,
//         canManageCompliance: false,
//       };
//     case "PAYER":
//       return {
//         canManageUsers: false,
//         canManageOrganizations: false,
//         canManagePayments: false,
//         canAcknowledgePayments: false,
//         canApprovePayments: false,
//         canViewReports: false,
//         canManageCompliance: false,
//       };
//     default:
//       return {
//         canManageUsers: false,
//         canManageOrganizations: false,
//         canManagePayments: false,
//         canAcknowledgePayments: false,
//         canApprovePayments: false,
//         canViewReports: false,
//         canManageCompliance: false,
//       };
//   }
// };

// interface AuthProviderProps {
//   children: ReactNode;
// }

// export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
//   const [user, setUser] = useState<User | null>(null);
//   const [isLoading, setIsLoading] = useState(true);
//   const [error, setError] = useState<AuthError | undefined>(undefined);
//   const [isLoginInProgress, setIsLoginInProgress] = useState(false);

//   // Initialize authentication on mount
//   useEffect(() => {
//     initializeAuth();

//     // Listen for token refresh failures
//     const handleTokenRefreshFailed = (event: CustomEvent) => {
//       console.error(
//         "Token refresh failed, user needs to re-authenticate:",
//         event.detail
//       );
//       setError({
//         code: "TOKEN_REFRESH_FAILED",
//         message: "Session expired. Please log in again.",
//         details: event.detail,
//       });
//       // Clear user state to force re-authentication
//       setUser(null);
//     };

//     window.addEventListener(
//       "tokenRefreshFailed",
//       handleTokenRefreshFailed as EventListener
//     );

//     // Cleanup
//     return () => {
//       window.removeEventListener(
//         "tokenRefreshFailed",
//         handleTokenRefreshFailed as EventListener
//       );
//       clearTokenRefreshTimer();
//     };
//   }, []);

//   const initializeAuth = async () => {
//     try {
//       setIsLoading(true);
//       setError(undefined);

//       // Initialize MSAL
//       await msalInstance.initialize();

//       // Check for existing tokens
//       const localToken = localStorage.getItem("local_auth_token");
//       const authType = localStorage.getItem("auth_type");

//       console.log("Init - Auth type:", authType);
//       console.log("Init - Local token exists:", !!localToken);
//       console.log("Init - MSAL logged in:", isLoggedIn());

//       // Try to restore session
//       if (localToken && authType === "local") {
//         try {
//           const currentUser = await authService.getCurrentUser();
//           setUser(currentUser);
//           console.log("Local session restored");
//         } catch (err) {
//           console.error("Failed to restore local session:", err);
//           // Clear invalid tokens
//           localStorage.removeItem("local_auth_token");
//           localStorage.removeItem("auth_type");
//         }
//       } else if (isLoggedIn()) {
//         try {
//           // Set auth type for Microsoft authentication
//           localStorage.setItem(AUTH_TYPE_KEY, "microsoft");
//           const currentUser = await authService.getCurrentUser();
//           setUser(currentUser);
//           console.log("Microsoft session restored");
//         } catch (err) {
//           console.error("Failed to restore Microsoft session:", err);
//           // Clear auth type if Microsoft session restoration fails
//           localStorage.removeItem("auth_type");
//         }
//       }
//     } catch (err) {
//       console.error("Auth initialization failed:", err);
//       setError({
//         code: "INIT_ERROR",
//         message: "Failed to initialize authentication",
//         details: err,
//       });
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Updated login function to support both local and Microsoft authentication
//   const login = async (options?: LoginOptions) => {
//     // Prevent multiple login attempts
//     if (isLoginInProgress) {
//       console.log("Login already in progress, skipping...");
//       return;
//     }

//     try {
//       setIsLoginInProgress(true);
//       setIsLoading(true);
//       setError(undefined);

//       if (options?.authMethod === "local" && options.credentials) {
//         // Local login
//         const { email, password } = options.credentials;
//         const localUser = await authService.localLogin(email, password);
//         setUser(localUser);
//       } else {
//         // Microsoft login (default)
//         try {
//           await msalInstance.handleRedirectPromise();
//         } catch (error) {
//           // Ignore redirect errors, continue with login
//           console.debug("Redirect promise error (expected):", error);
//         }
//         await signIn();
//         const currentUser = await authService.getCurrentUser();
//         setUser(currentUser);
//       }
//     } catch (err) {
//       console.error("Login failed:", err);
//       setError({
//         code: "LOGIN_ERROR",
//         message: "Login failed",
//         details: err,
//       });
//       throw err;
//     } finally {
//       setIsLoading(false);
//       setIsLoginInProgress(false);
//     }
//   };

//   const logout = async (options?: LogoutOptions) => {
//     try {
//       setIsLoading(true);
//       setError(undefined);

//       // Clear token refresh timer
//       clearTokenRefreshTimer();

//       // Clear local auth tokens
//       authService.clearTokens();

//       // Perform Microsoft logout with proper cleanup
//       await signOut(options?.useRedirect || false);

//       // Clear user state
//       setUser(null);

//       console.log("Logout completed successfully");
//     } catch (err) {
//       console.error("Logout failed:", err);

//       // Even if logout fails, clear local state
//       setUser(null);
//       clearTokenRefreshTimer();

//       setError({
//         code: "LOGOUT_ERROR",
//         message: "Logout failed",
//         details: err,
//       });

//       // Don't throw error for logout - always clear state
//       console.log("Logout completed with errors, but state cleared");
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   // Manual token refresh function
//   const refreshToken = async (): Promise<boolean> => {
//     try {
//       setError(undefined);

//       const newToken = await refreshAccessToken();
//       if (newToken) {
//         console.log("Token refreshed successfully");
//         return true;
//       } else {
//         console.log("Token refresh failed - user needs to re-authenticate");
//         setError({
//           code: "TOKEN_REFRESH_FAILED",
//           message: "Session expired. Please log in again.",
//           details: "Token refresh returned null",
//         });
//         setUser(null);
//         return false;
//       }
//     } catch (err) {
//       console.error("Manual token refresh failed:", err);
//       setError({
//         code: "TOKEN_REFRESH_FAILED",
//         message: "Failed to refresh session. Please log in again.",
//         details: err,
//       });
//       setUser(null);
//       return false;
//     }
//   };

//   const refreshUser = async () => {
//     try {
//       setError(undefined);
//       const currentUser = await authService.getCurrentUser();
//       setUser(currentUser);
//     } catch (err) {
//       console.error("Failed to refresh user:", err);
//       setError({
//         code: "REFRESH_ERROR",
//         message: "Failed to refresh user data",
//         details: err,
//       });
//       throw err;
//     }
//   };

//   const hasRole = (role: UserRole | UserRole[]): boolean => {
//     if (!user) return false;

//     if (Array.isArray(role)) {
//       return role.includes(user.role);
//     }

//     return user.role === role;
//   };

//   const hasAnyRole = (roles: UserRole[]): boolean => {
//     if (!user) return false;
//     return roles.includes(user.role);
//   };

//   const isAuthenticated = !!user;
//   const permissions = user
//     ? getPermissionsForRole(user.role)
//     : getPermissionsForRole("PAYER");

//   const contextValue: AuthContextType = {
//     user,
//     isLoading,
//     isAuthenticated,
//     login,
//     logout,
//     refreshUser,
//     refreshToken,
//     hasRole,
//     hasAnyRole,
//     permissions,
//     error,
//   };

//   return (
//     <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
//   );
// };

// // Custom hook to use the AuthContext
// export const useAuth = (): AuthContextType => {
//   const context = useContext(AuthContext);
//   if (context === undefined) {
//     throw new Error("useAuth must be used within an AuthProvider");
//   }
//   return context;
// };

// export { AuthContext };
