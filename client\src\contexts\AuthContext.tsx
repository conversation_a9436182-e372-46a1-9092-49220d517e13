import React, { useEffect, useState, useContext, type ReactNode } from "react";
import type {
  User,
  AuthContextType,
  UserRole,
  Permissions,
  LogoutOptions,
  AuthError,
  LoginOptions,
} from "../types/auth";
import {
  signIn,
  signOut,
  isLoggedIn,
  refreshAccessToken,
  clearTokenRefreshTimer,
} from "../services/msalAuthService";
import { authService } from "../services/authService";
import { msalInstance } from "../auth/msalConfig";
import {
  AuthContext,
  AUTH_TYPE_KEY,
  LOCAL_TOKEN_KEY,
} from "./AuthContext.context";

// Permission mapping based on roles
const getPermissionsForRole = (role: UserRole): Permissions => {
  switch (role) {
    case "JTB_ADMIN":
      return {
        canManageUsers: true,
        canManageOrganizations: true,
        canManagePayments: true,
        canAcknowledgePayments: true,
        canApprovePayments: true,
        canViewReports: true,
        canManageCompliance: true,
      };
    case "SENIOR_FINANCE_OFFICER":
      return {
        canManageUsers: false,
        canManageOrganizations: false,
        canManagePayments: true,
        canAcknowledgePayments: true,
        canApprovePayments: true,
        canViewReports: true,
        canManageCompliance: true,
      };
    case "FINANCE_OFFICER":
      return {
        canManageUsers: false,
        canManageOrganizations: false,
        canManagePayments: true,
        canAcknowledgePayments: true,
        canApprovePayments: false,
        canViewReports: true,
        canManageCompliance: false,
      };
    case "PAYER":
      return {
        canManageUsers: false,
        canManageOrganizations: false,
        canManagePayments: false,
        canAcknowledgePayments: false,
        canApprovePayments: false,
        canViewReports: false,
        canManageCompliance: false,
      };
    default:
      return {
        canManageUsers: false,
        canManageOrganizations: false,
        canManagePayments: false,
        canAcknowledgePayments: false,
        canApprovePayments: false,
        canViewReports: false,
        canManageCompliance: false,
      };
  }
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<AuthError | undefined>(undefined);
  const [isLoginInProgress, setIsLoginInProgress] = useState(false);

  // Initialize authentication on mount
  useEffect(() => {
    initializeAuth();

    // Listen for token refresh failures
    const handleTokenRefreshFailed = (event: CustomEvent) => {
      console.error(
        "Token refresh failed, user needs to re-authenticate:",
        event.detail
      );
      setError({
        code: "TOKEN_REFRESH_FAILED",
        message: "Session expired. Please log in again.",
        details: event.detail,
      });
      // Clear user state to force re-authentication
      setUser(null);
    };

    window.addEventListener(
      "tokenRefreshFailed",
      handleTokenRefreshFailed as EventListener
    );

    // Cleanup
    return () => {
      window.removeEventListener(
        "tokenRefreshFailed",
        handleTokenRefreshFailed as EventListener
      );
      clearTokenRefreshTimer();
    };
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      setError(undefined);

      // Initialize MSAL
      await msalInstance.initialize();

      // Check for existing tokens
      const localToken = localStorage.getItem("local_auth_token");
      const authType = localStorage.getItem("auth_type");

      console.log("Init - Auth type:", authType);
      console.log("Init - Local token exists:", !!localToken);
      console.log("Init - MSAL logged in:", isLoggedIn());

      // Try to restore session
      if (localToken && authType === "local") {
        try {
          const currentUser = await authService.getCurrentUser();
          setUser(currentUser);
          console.log("Local session restored");
        } catch (err) {
          console.error("Failed to restore local session:", err);
          // Clear invalid tokens
          localStorage.removeItem("local_auth_token");
          localStorage.removeItem("auth_type");
        }
      } else if (isLoggedIn()) {
        try {
          // Set auth type for Microsoft authentication
          localStorage.setItem(AUTH_TYPE_KEY, "microsoft");
          const currentUser = await authService.getCurrentUser();
          setUser(currentUser);
          console.log("Microsoft session restored");
        } catch (err) {
          console.error("Failed to restore Microsoft session:", err);
          // Clear auth type if Microsoft session restoration fails
          localStorage.removeItem("auth_type");
        }
      }
    } catch (err) {
      console.error("Auth initialization failed:", err);
      setError({
        code: "INIT_ERROR",
        message: "Failed to initialize authentication",
        details: err,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Updated login function to support both local and Microsoft authentication
  const login = async (options?: LoginOptions) => {
    // Prevent multiple login attempts
    if (isLoginInProgress) {
      console.log("Login already in progress, skipping...");
      return;
    }

    try {
      setIsLoginInProgress(true);
      setIsLoading(true);
      setError(undefined);

      if (options?.authMethod === "local" && options.credentials) {
        // Local login
        const { email, password } = options.credentials;
        const localUser = await authService.localLogin(email, password);
        setUser(localUser);
      } else {
        // Microsoft login (default)
        try {
          await msalInstance.handleRedirectPromise();
        } catch (error) {
          // Ignore redirect errors, continue with login
          console.debug("Redirect promise error (expected):", error);
        }
        await signIn();
        const currentUser = await authService.getCurrentUser();
        setUser(currentUser);
      }
    } catch (err) {
      console.error("Login failed:", err);
      setError({
        code: "LOGIN_ERROR",
        message: "Login failed",
        details: err,
      });
      throw err;
    } finally {
      setIsLoading(false);
      setIsLoginInProgress(false);
    }
  };

  const logout = async (options?: LogoutOptions) => {
    try {
      setIsLoading(true);
      setError(undefined);

      // Clear token refresh timer
      clearTokenRefreshTimer();

      // Clear local auth tokens
      authService.clearTokens();

      // Perform Microsoft logout with proper cleanup
      await signOut(options?.useRedirect || false);

      // Clear user state
      setUser(null);

      console.log("Logout completed successfully");
    } catch (err) {
      console.error("Logout failed:", err);

      // Even if logout fails, clear local state
      setUser(null);
      clearTokenRefreshTimer();

      setError({
        code: "LOGOUT_ERROR",
        message: "Logout failed",
        details: err,
      });

      // Don't throw error for logout - always clear state
      console.log("Logout completed with errors, but state cleared");
    } finally {
      setIsLoading(false);
    }
  };

  // Manual token refresh function
  const refreshToken = async (): Promise<boolean> => {
    try {
      setError(undefined);

      const newToken = await refreshAccessToken();
      if (newToken) {
        console.log("Token refreshed successfully");
        return true;
      } else {
        console.log("Token refresh failed - user needs to re-authenticate");
        setError({
          code: "TOKEN_REFRESH_FAILED",
          message: "Session expired. Please log in again.",
          details: "Token refresh returned null",
        });
        setUser(null);
        return false;
      }
    } catch (err) {
      console.error("Manual token refresh failed:", err);
      setError({
        code: "TOKEN_REFRESH_FAILED",
        message: "Failed to refresh session. Please log in again.",
        details: err,
      });
      setUser(null);
      return false;
    }
  };

  const refreshUser = async () => {
    try {
      setError(undefined);
      const currentUser = await authService.getCurrentUser();
      setUser(currentUser);
    } catch (err) {
      console.error("Failed to refresh user:", err);
      setError({
        code: "REFRESH_ERROR",
        message: "Failed to refresh user data",
        details: err,
      });
      throw err;
    }
  };

  const hasRole = (role: UserRole | UserRole[]): boolean => {
    if (!user) return false;

    if (Array.isArray(role)) {
      return role.includes(user.role);
    }

    return user.role === role;
  };

  const hasAnyRole = (roles: UserRole[]): boolean => {
    if (!user) return false;
    return roles.includes(user.role);
  };

  const isAuthenticated = !!user;
  const permissions = user
    ? getPermissionsForRole(user.role)
    : getPermissionsForRole("PAYER");

  const contextValue: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    logout,
    refreshUser,
    refreshToken,
    hasRole,
    hasAnyRole,
    permissions,
    error,
  };

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};

// Custom hook to use the AuthContext
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};

export { AuthContext };
