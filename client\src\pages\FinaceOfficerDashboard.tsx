import React from 'react';
import {
  Users,
  CreditCard,
  FileText,
  DollarSign,
  Receipt,
  Eye,
  Building2,
  User,
  Calendar,
  BarChart3
} from 'lucide-react';
import type { LucideIcon } from 'lucide-react'
import Navbar from '../components/NavBaar';
import Overview from '../components/FinanceOfficer/Overview';
import PaymentTypes from '../components/FinanceOfficer/PaymentTypes';
import ReceiptComponent from '../components/FinanceOfficer/Receipt';
import PayerProfiles from '../components/FinanceOfficer/PayerProfiles';
import PaymentProfiles from '../components/FinanceOfficer/PaymentProfiles';
import PaymentTracking from '../components/FinanceOfficer/PaymentTracking';
import OrganizationsPage from './OrganizationsPage';
import PaymentsPage from './PaymentsPage';
import PaymentProfilesPage from './PaymentProfilesPage';
import PaymentSchedulesPage from './PaymentSchedulesPage';
import ReceiptsPage from './ReceiptsPage';
import ReportsPage from './ReportsPage';
import NotificationsPage from './NotificationsPage';
import { LogoutButton } from '../components/Auth/LogoutButton';
import SessionStatus from '../components/Auth/SessionStatus';
import { NavLink } from 'react-router-dom';
import { Navigate, Route, Routes } from 'react-router-dom';
import Footer from '../components/Footer';

interface TabButtonProps {
  id: string;
  label: string;
  icon: LucideIcon;
}
const TabButton: React.FC<TabButtonProps> = ({ id, label, icon: Icon}) => (
    <NavLink
        to={`/finance-officer-dashboard/${id}`}
        className={({isActive})=> `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
          isActive 
            ? 'bg-[#045024] text-white shadow-md' 
            : 'text-[#045024] hover:bg-[#2aa45c] hover:bg-opacity-10'
        }`}
    >
      <Icon size={18} />
      {label}
    </NavLink>
  );


const FinanceOfficerDashboard: React.FC = () => {
  
  return (
    <>
      <Navbar />
      <div className="min-h-screen pt-16" style={{ backgroundColor: '#dddeda' }}>
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold mb-2" style={{ color: '#045024' }}>
                Finance Officer Dashboard
              </h1>
              <p className="text-gray-600">Manage payments, payers, and financial operations</p>
            </div>
            <div className="flex items-center space-x-4">
              <SessionStatus />
              <LogoutButton size="sm" variant="outline" />
            </div>
          </div>

          <div className="py-8">

             <div >
                <div className="mb-8">
                  <div className="flex flex-wrap gap-2">
                    <TabButton id="overview" label="Overview" icon={Eye}/>
                    <TabButton id="organizations" label="Organizations" icon={Building2} />
                    <TabButton id="payments" label="Payments" icon={CreditCard} />
                    <TabButton id="payment-profiles" label="Payment Profiles" icon={User} />
                    <TabButton id="payment-schedules" label="Payment Schedules" icon={Calendar} />
                    <TabButton id="receipts" label="Receipts" icon={Receipt} />
                    <TabButton id="reports" label="Reports" icon={BarChart3} />
                    <TabButton id="payment-types" label="Payment Types" icon={CreditCard} />
                    <TabButton id="payer-profiles" label="Payer Profiles" icon={Users} />
                    <TabButton id="tracking" label="Tracking" icon={DollarSign} />
                  </div>
                </div>
            </div>

             <div className="min-h-96">
              <Routes>
              <Route path='' element={<Navigate to="overview" />} />
                <Route path='overview' element={<Overview setActiveTab={() => {}} />} />
                <Route path='organizations/*' element={<OrganizationsPage />} />
                <Route path='payments/*' element={<PaymentsPage />} />
                <Route path='payment-profiles/*' element={<PaymentProfilesPage />} />
                <Route path='payment-schedules/*' element={<PaymentSchedulesPage />} />
                <Route path='receipts/*' element={<ReceiptsPage />} />
                <Route path='reports/*' element={<ReportsPage />} />
                <Route path='notifications/*' element={<NotificationsPage />} />
                <Route path='payment-types' element={<PaymentTypes />} />
                <Route path='payer-profiles' element={<PayerProfiles />} />
                <Route path='tracking' element={<PaymentTracking />} />
              </Routes>

          </div>
          </div>
        </div>
      </div>
      <Footer/>
    </>
  );
};

export default FinanceOfficerDashboard;