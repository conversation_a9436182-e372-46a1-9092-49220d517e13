// import React, { useState } from 'react';
// import { 
//   // CheckSquare, 
//   Clock, 
//   History, 
 
// } from 'lucide-react';
// import type { LucideIcon } from 'lucide-react';
// import Navbar from '../components/NavBaar';
// // import PaymentApprovalPage from '../components/SeniorOfficer/PaymentApproval';
// import ApprovalHistoryPage from '../components/SeniorOfficer/HistoryPayment';
// import PendingApprovalsDashboard from '../components/SeniorOfficer/PendingApproval';
// import PaymentApprovalSystem from '../components/SeniorOfficer/PendingApproval';

// interface TabButtonProps {
//   id: string;
//   label: string;
//   icon: LucideIcon;
//   active: boolean;
//   onClick: (id: string) => void;
// }

// const SeniorFinanceOfficerDashboard: React.FC = () => {
//   const [activeTab, setActiveTab] = useState<string>('pending-approvals');

//   const TabButton: React.FC<TabButtonProps> = ({ id, label, icon: Icon, active, onClick }) => (
//     <button
//       onClick={() => onClick(id)}
//       className={`flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
//         active 
//           ? 'bg-[#045024] text-white shadow-md' 
//           : 'text-[#045024] hover:bg-[#2aa45c] hover:bg-opacity-10'
//       }`}
//     >
//       <Icon size={18} />
//       {label}
//     </button>
//   );

//   const renderContent = () => {
//     switch (activeTab) {
//       case 'pending-approvals': 
//         return <PaymentApprovalSystem />;
//       // case 'payment-approval': 
//       //   return <PaymentApprovalPage />;
//       case 'approval-history': 
//         return <ApprovalHistoryPage />;
//       default:
//         return <PendingApprovalsDashboard />;
//     }
//   };

//   const tabs = [
//     { id: 'pending-approvals', label: 'Pending Approvals', icon: Clock },
//     // { id: 'payment-approval', label: 'Payment Approval', icon: CheckSquare },
//     { id: 'approval-history', label: 'Approval History', icon: History }
//   ];

//   return(
//     <>
//         return (
//     <>
//       <Navbar />
//       <div className="min-h-screen pt-16" style={{ backgroundColor: '#dddeda' }}>
//         <div className="max-w-7xl mx-auto px-4 py-8">
//           {/* Remove this extra wrapper */}
//           {/* <div className="mb-8"> */}
//           <h1 className="text-3xl font-bold mb-2" style={{ color: '#045024' }}>
//             Senior Finance Officer Dashboard
//           </h1>
//           <p className="text-gray-600">Review and approve payment requests</p>
//           {/* </div> */}

//           <div className="py-8">
//             <div className="flex flex-wrap gap-2 mb-8">
//               {tabs.map(tab => (
//                 <TabButton
//                   key={tab.id}
//                   id={tab.id}
//                   label={tab.label}
//                   icon={tab.icon}
//                   active={activeTab === tab.id}
//                   onClick={setActiveTab}
//                 />
//               ))}
//             </div>

//             {renderContent()}
//           </div>
//         </div>
//       </div>

//     </>
//   );
//     </>
//   )}

//   export default SeniorFinanceOfficerDashboard;

import React from 'react';
import {
  Clock,
  History,
  CreditCard,
  Receipt,
  BarChart3,
} from 'lucide-react';
import type { LucideIcon } from 'lucide-react';
import Navbar from '../components/NavBaar';
import ApprovalHistoryPage from '../components/SeniorOfficer/HistoryPayment';
import PaymentApprovalSystem from '../components/SeniorOfficer/PendingApproval';
import PaymentsPage from './PaymentsPage';
import ReceiptsPage from './ReceiptsPage';
import ReportsPage from './ReportsPage';
import { LogoutButton } from '../components/Auth/LogoutButton';
import SessionStatus from '../components/Auth/SessionStatus';
import { NavLink, Navigate, Route, Routes } from 'react-router-dom';
import Footer from '../components/Footer';

interface TabButtonProps {
  id: string;
  label: string;
  icon: LucideIcon;
}

const TabButton: React.FC<TabButtonProps> = ({ id, label, icon: Icon }) => (
  <NavLink
    to={`/senior-finance-officer-dashboard/${id}`}
    className={({ isActive }) => `flex items-center gap-2 px-4 py-2 rounded-lg font-medium transition-all ${
      isActive 
        ? 'bg-[#045024] text-white shadow-md' 
        : 'text-[#045024] hover:bg-[#2aa45c] hover:bg-opacity-10'
    }`}
  >
    <Icon size={18} />
    {label}
  </NavLink>
);

const SeniorFinanceOfficerDashboard: React.FC = () => {
  const tabs = [
    { id: 'pending-approvals', label: 'Pending Approvals', icon: Clock },
    { id: 'payments', label: 'Payments', icon: CreditCard },
    { id: 'receipts', label: 'Receipts', icon: Receipt },
    { id: 'reports', label: 'Reports', icon: BarChart3 },
    { id: 'approval-history', label: 'Approval History', icon: History }
  ];

  return (
    <>
      <Navbar />
      <div className="min-h-screen pt-16" style={{ backgroundColor: '#dddeda' }}>
        <div className="max-w-7xl mx-auto px-4 py-8">
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-3xl font-bold mb-2" style={{ color: '#045024' }}>
                Senior Finance Officer Dashboard
              </h1>
              <p className="text-gray-600">Review and approve payment requests</p>
            </div>
            <div className="flex items-center space-x-4">
              <SessionStatus />
              <LogoutButton size="sm" variant="outline" />
            </div>
          </div>

          <div className="py-8">
            <div className="mb-8">
              <div className="flex flex-wrap gap-2">
                {tabs.map(tab => (
                  <TabButton
                    key={tab.id}
                    id={tab.id}
                    label={tab.label}
                    icon={tab.icon}
                  />
                ))}
              </div>
            </div>

            <div className="min-h-96">
              <Routes>
                <Route path="" element={<Navigate to="pending-approvals" />} />
                <Route path="pending-approvals" element={<PaymentApprovalSystem />} />
                <Route path="payments/*" element={<PaymentsPage />} />
                <Route path="receipts/*" element={<ReceiptsPage />} />
                <Route path="reports/*" element={<ReportsPage />} />
                <Route path="approval-history" element={<ApprovalHistoryPage />} />
              </Routes>
            </div>
          </div>
        </div>
      </div>
      <Footer/>
    </>
  );
};

export default SeniorFinanceOfficerDashboard;