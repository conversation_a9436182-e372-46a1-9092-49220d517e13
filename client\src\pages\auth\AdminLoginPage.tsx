import React, { useState, useEffect } from 'react';
import { Shield } from 'lucide-react';
import <PERSON><PERSON>eiptLogo from "../../assets/Ereceipt-logo.png";
import CoatOfArmLogo from "../../assets/coat-of-arm-preview.png";
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { signIn, getAccessToken } from '../../services/msalAuthService';

const AdminLoginPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);


  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();

  // Handle role-based navigation after successful login
  useEffect(() => {
    if (isAuthenticated && user) {
      switch (user.role) {
        case 'JTB_ADMIN':
          navigate('/admin-dashboard');
          break;
        case 'FINANCE_OFFICER':
          navigate('/finance-officer-dashboard');
          break;
        case 'SENIOR_FINANCE_OFFICER':
          navigate('/senior-finance-officer-dashboard');
          break;
        case 'PAYER':
          navigate('/payerdashboard');
          break;
        default:
          console.warn('Unknown user role:', user.role);
          navigate('/admin-dashboard'); // Default to admin for admin login page
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleMicrosoftLogin = async () => {
    setIsLoading(true);
    setError('');

    try {
      // Step 1: Authenticate with Microsoft to get token
      console.log('Step 1: Authenticating with Microsoft...');
      await signIn();

      // Step 2: Get access token
      console.log('Step 2: Getting access token...');
      const token = await getAccessToken();

      if (!token) {
        throw new Error('Failed to get access token from Microsoft');
      }

      // Step 3: Call admin setup endpoint with token
      console.log('Step 3: Calling admin setup endpoint...');
      const response = await fetch(
        `${import.meta.env.VITE_API_BASE_URL}/AdminSetup/initialize`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to initialize admin account');
      }

      const data = await response.json();
      console.log('Admin initialized:', data);

      // Show success message and redirect
      alert('Admin account created successfully! Redirecting to admin dashboard...');
      navigate('/admin-dashboard');

    } catch (error: unknown) {
      console.error("Admin setup error:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };



  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12" 
         style={{ background: 'linear-gradient(135deg, #045024 0%, #076934 50%, #2aa45c 100%)' }}>
      
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25% 25%, rgba(221, 222, 218, 0.1) 0%, transparent 50%),
                           radial-gradient(circle at 75% 75%, rgba(221, 222, 218, 0.1) 0%, transparent 50%)`
        }} />
      </div>

      {/* Main Container with Side Images */}
      <div className="w-full max-w-6xl relative z-10 flex items-center justify-center gap-8">
        
        {/* Left Side Image */}
        <div className="hidden lg:flex flex-1 justify-center">
          <div className="w-80 h-96 rounded-2xl overflow-hidden backdrop-blur-sm relative shadow-lg">
            <img 
              src={EReceiptLogo} 
              alt="Left decorative image for admin"
              className="w-full h-full object-cover"
              style={{ 
                filter: 'brightness(0.8) contrast(1.1)',
                mixBlendMode: 'overlay'
              }}
            />
            {/* Fallback content when image is not available */}
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-700 to-green-900 opacity-20">
              <div className="text-center text-white">
                <Shield className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-semibold opacity-70">Left Image</p>
              </div>
            </div>
          </div>
        </div>

        {/* Admin Login Form Container */}
        <div className="w-full max-w-md">
          <div className="backdrop-blur-sm rounded-2xl p-8 shadow-2xl"
               style={{ backgroundColor: 'rgba(221, 222, 218, 0.15)' }}>
            {/* Microsoft SSO Section */}
            <div className="space-y-6">
              <div className="text-center py-8">
                <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center"
                     style={{ backgroundColor: 'rgba(221, 222, 218, 0.2)' }}>
                  <svg className="w-10 h-10" viewBox="0 0 24 24" fill="none">
                    <path d="M5.8 3H12.2C12.37 3 12.53 3.07 12.65 3.18C12.76 3.29 12.83 3.45 12.83 3.62V10.38C12.83 10.55 12.76 10.71 12.65 10.82C12.53 10.93 12.37 11 12.2 11H5.8C5.63 11 5.47 10.93 5.35 10.82C5.24 10.71 5.17 10.55 5.17 10.38V3.62C5.17 3.45 5.24 3.29 5.35 3.18C5.47 3.07 5.63 3 5.8 3Z" fill="#F25022"/>
                    <path d="M12.2 13H18.6C18.77 13 18.93 13.07 19.05 13.18C19.16 13.29 19.23 13.45 19.23 13.62V20.38C19.23 20.55 19.16 20.71 19.05 20.82C18.93 20.93 18.77 21 18.6 21H12.2C12.03 21 11.87 20.93 11.75 20.82C11.64 20.71 11.57 20.55 11.57 20.38V13.62C11.57 13.45 11.64 13.29 11.75 13.18C11.87 13.07 12.03 13 12.2 13Z" fill="#00A4EF"/>
                    <path d="M5.8 13H12.2C12.37 13 12.53 13.07 12.65 13.18C12.76 13.29 12.83 13.45 12.83 13.62V20.38C12.83 20.55 12.76 20.71 12.65 20.82C12.53 10.93 12.37 11 12.2 11H5.8C5.63 11 5.47 10.93 5.35 10.82C5.24 10.71 5.17 10.55 5.17 10.38V3.62C5.17 3.45 5.24 3.29 5.35 3.18C5.47 3.07 5.63 3 5.8 3Z" fill="#FFB900"/>
                    <path d="M12.2 3H18.6C18.77 3 18.93 3.07 19.05 3.18C19.16 3.29 19.23 3.45 19.23 3.62V10.38C19.23 10.55 19.16 10.71 19.05 10.82C18.93 10.93 18.77 11 18.6 11H12.2C12.03 11 11.87 10.93 11.75 10.82C11.64 10.71 11.57 10.55 11.57 10.38V3.62C11.57 3.45 11.64 3.29 11.75 3.18C11.87 3.07 12.03 3 12.2 3Z" fill="#737373"/>
                  </svg>
                </div>
                <h3 className="text-2xl font-semibold mb-3" style={{ color: '#dddeda' }}>
                  Initialize Admin Account
                </h3>
                <p className="text-sm opacity-80 mb-8 leading-relaxed" style={{ color: '#dddeda' }}>
                  Use your Microsoft account to create the first admin user.
                  <br />
                  This is a one-time setup process for system initialization.
                </p>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-800 bg-opacity-50 text-white rounded-md">
                  {error}
                </div>
              )}

              <button
                onClick={handleMicrosoftLogin}
                disabled={isLoading}
                className="w-full py-4 px-6 rounded-lg font-medium transition-all duration-200 transform hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center justify-center space-x-3"
                style={{ 
                  backgroundColor: '#dddeda',
                  color: '#045024'
                }}
              >
                {isLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-current"></div>
                    <span className="text-lg">Setting up Admin Account...</span>
                  </>
                ) : (
                  <>
                    <svg className="w-6 h-6" viewBox="0 0 24 24" fill="none">
                      <path d="M5.8 3H12.2C12.37 3 12.53 3.07 12.65 3.18C12.76 3.29 12.83 3.45 12.83 3.62V10.38C12.83 10.55 12.76 10.71 12.65 10.82C12.53 10.93 12.37 11 12.2 11H5.8C5.63 11 5.47 10.93 5.35 10.82C5.24 10.71 5.17 10.55 5.17 10.38V3.62C5.17 3.45 5.24 3.29 5.35 3.18C5.47 3.07 5.63 3 5.8 3Z" fill="#F25022"/>
                      <path d="M12.2 13H18.6C18.77 13 18.93 13.07 19.05 13.18C19.16 13.29 19.23 13.45 19.23 13.62V20.38C19.23 20.55 19.16 20.71 19.05 20.82C18.93 20.93 18.77 21 18.6 21H12.2C12.03 21 11.87 20.93 11.75 20.82C11.64 20.71 11.57 20.55 11.57 20.38V13.62C11.57 13.45 11.64 3.29 11.75 3.18C11.87 3.07 12.03 3 12.2 3Z" fill="#FFB900"/>
                      <path d="M12.2 3H18.6C18.77 3 18.93 3.07 19.05 3.18C19.16 3.29 19.23 3.45 19.23 3.62V10.38C19.23 10.55 19.16 10.71 19.05 10.82C18.93 10.93 18.77 11 18.6 11H12.2C12.03 11 11.87 10.93 11.75 10.82C11.64 10.71 11.57 10.55 11.57 10.38V3.62C11.57 3.45 11.64 3.29 11.75 3.18C11.87 3.07 12.03 3 12.2 3Z" fill="#737373"/>
                    </svg>
                    <span className="text-lg">Initialize Admin with Microsoft</span>
                  </>
                )}
              </button>

              {/* Security Notice */}
              <div className="mt-8 p-4 rounded-lg" style={{ backgroundColor: 'rgba(221, 222, 218, 0.1)' }}>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <Shield className="w-5 h-5" style={{ color: '#2aa45c' }} />
                  </div>
                  <div>
                    <h4 className="text-sm font-medium mb-1" style={{ color: '#dddeda' }}>
                      Secure Administrator Access
                    </h4>
                    <p className="text-xs opacity-80 leading-relaxed" style={{ color: '#dddeda' }}>
                      This portal requires valid Microsoft organization credentials. 
                      All login attempts are logged and monitored for security purposes.
                    </p>
                  </div>
                </div>
              </div>

              {/* Help Section */}
              <div className="text-center mt-6">
                <p className="text-sm opacity-80 mb-2" style={{ color: '#dddeda' }}>
                  Having trouble signing in?
                </p>
                <button
                  type="button"
                  className="text-sm hover:underline transition-colors"
                  style={{ color: '#dddeda' }}
                >
                  Contact IT Support
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Right Side Image */}
        <div className="hidden lg:flex flex-1 justify-center">
          <div className="w-80 h-60 rounded-2xl overflow-hidden backdrop-blur-sm relative shadow-lg">
            <img 
              src={CoatOfArmLogo} 
              alt="Right decorative image for admin"
              className="w-full h-full object-cover"
              style={{ 
                filter: 'brightness(0.8) contrast(1.1)',
                mixBlendMode: 'overlay'
              }}
            />
            {/* Fallback content when image is not available */}
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-bl from-green-700 to-green-900 opacity-20">
              <div className="text-center text-white">
                <Shield className="w-16 h-16 mx-auto mb-4 opacity-50" />
                <p className="text-lg font-semibold opacity-70">Right Image</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2">
        <p className="text-xs opacity-70" style={{ color: '#dddeda' }}>
          🔒 One-time admin setup. This endpoint becomes unavailable after first admin is created.
        </p>
      </div>
    </div>
  );
};

export default AdminLoginPage;
