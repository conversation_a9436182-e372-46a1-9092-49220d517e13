import React, { useState, useEffect } from 'react';
import { useAuth } from '../../hooks/useAuth';
import { useNavigate } from 'react-router-dom';
import { authDetectionService } from '../../services/authDetectionService';

type LoginStep = 'email' | 'password' | 'microsoft' | 'loading';

const LoginPage: React.FC = () => {
  const { user, isAuthenticated, login, isLoading } = useAuth();
  const navigate = useNavigate();
  
  // Multi-step state
  const [step, setStep] = useState<LoginStep>('email');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [authMethod, setAuthMethod] = useState<'microsoft' | 'local' | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isDetecting, setIsDetecting] = useState(false);

  // Auto-redirect authenticated users
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('User authenticated, redirecting based on role:', user.role);
      
      switch (user.role) {
        case 'JTB_ADMIN':
          navigate('/admin-dashboard');
          break;
        case 'SENIOR_FINANCE_OFFICER':
          navigate('/senior-finance-officer-dashboard');
          break;
        case 'FINANCE_OFFICER':
          navigate('/finance-officer-dashboard');
          break;
        case 'PAYER':
          navigate('/payer-dashboard');
          break;
        default:
          navigate('/payer-dashboard');
      }
    }
  }, [isAuthenticated, user, navigate]);

  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email.trim()) {
      setError('Please enter your email address');
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return;
    }

    try {
      setIsDetecting(true);
      setError(null);
      
      const detection = await authDetectionService.detectAuthMethod(email);
      
      if (!detection.authMethod) {
        setError(detection.message || 'No account found for this email address');
        return;
      }

      setAuthMethod(detection.authMethod);
      
      if (detection.authMethod === 'local') {
        setStep('password');
      } else {
        setStep('microsoft');
        // Auto-trigger Microsoft login
        setTimeout(() => handleMicrosoftLogin(), 1000);
      }
      
    } catch (err) {
      console.error('Email detection failed:', err);
      setError(err instanceof Error ? err.message : 'Failed to verify email');
    } finally {
      setIsDetecting(false);
    }
  };

  const handlePasswordSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!password) {
      setError('Please enter your password');
      return;
    }

    try {
      setError(null);
      await login({
        authMethod: 'local',
        credentials: { email, password }
      });
    } catch (err) {
      console.error('Local login failed:', err);
      setError(err instanceof Error ? err.message : 'Login failed');
    }
  };

  const handleMicrosoftLogin = async () => {
    try {
      setError(null);
      await login({ authMethod: 'microsoft' });
    } catch (err) {
      console.error('Microsoft login failed:', err);
      setError(err instanceof Error ? err.message : 'Microsoft login failed');
    }
  };

  const handleBackToEmail = () => {
    setStep('email');
    setPassword('');
    setAuthMethod(null);
    setError(null);
  };

  if (isLoading && step !== 'email') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Signing you in...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12" 
         style={{ background: 'linear-gradient(135deg, #045024 0%, #076934 50%, #2aa45c 100%)' }}>
      
      <div className="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow-xl">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">Sign In</h2>
          <p className="mt-2 text-gray-600">
            {step === 'email' && 'Enter your email to continue'}
            {step === 'password' && 'Enter your password'}
            {step === 'microsoft' && 'Redirecting to Microsoft...'}
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Step 1: Email Input */}
        {step === 'email' && (
          <form onSubmit={handleEmailSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your email address"
                disabled={isDetecting}
                autoFocus
              />
            </div>
            <button
              type="submit"
              disabled={isDetecting}
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isDetecting ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Checking...
                </div>
              ) : (
                'Continue'
              )}
            </button>
          </form>
        )}

        {/* Step 2: Password Input (Local Auth) */}
        {step === 'password' && (
          <div className="space-y-6">
            <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
              <strong>Email:</strong> {email}
              <button
                onClick={handleBackToEmail}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                Change
              </button>
            </div>
            
            <form onSubmit={handlePasswordSubmit} className="space-y-4">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your password"
                  disabled={isLoading}
                  autoFocus
                />
              </div>
              <button
                type="submit"
                disabled={isLoading}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </div>
                ) : (
                  'Sign In'
                )}
              </button>
            </form>
          </div>
        )}

        {/* Step 3: Microsoft Redirect */}
        {step === 'microsoft' && (
          <div className="space-y-6 text-center">
            <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md">
              <strong>Email:</strong> {email}
              <button
                onClick={handleBackToEmail}
                className="ml-2 text-blue-600 hover:text-blue-800"
              >
                Change
              </button>
            </div>
            
            <div className="space-y-4">
              <div className="animate-pulse">
                <div className="h-12 w-12 bg-blue-100 rounded-full mx-auto flex items-center justify-center">
                  <svg className="w-6 h-6 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                  </svg>
                </div>
              </div>
              <p className="text-gray-600">
                This email uses Microsoft authentication.<br/>
                You'll be redirected to sign in with your Microsoft account.
              </p>
              <button
                onClick={handleMicrosoftLogin}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Continue to Microsoft
              </button>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            {authMethod === 'microsoft' 
              ? 'For JTB staff and administrators' 
              : authMethod === 'local'
              ? 'For taxpayers and external users'
              : 'Enter your email to get started'
            }
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
