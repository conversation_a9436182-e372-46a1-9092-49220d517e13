using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Final_E_Receipt.Authentication.Services;
using Final_E_Receipt.Authentication.DTOs;
using Final_E_Receipt.Authentication.Models;
using Final_E_Receipt.Notifications.Services;

namespace Final_E_Receipt.Authentication.Controllers
{
    [ApiController]
    [Route("api/user-invitations")]
    [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER")]
    public class UserInvitationController : ControllerBase
    {
        private readonly AuthenticationService _authService;
        private readonly CentralizedEmailService _emailService;
        private readonly ILogger<UserInvitationController> _logger;

        public UserInvitationController(
            AuthenticationService authService,
            CentralizedEmailService emailService,
            ILogger<UserInvitationController> logger)
        {
            _authService = authService;
            _emailService = emailService;
            _logger = logger;
        }

        /// <summary>
        /// Invite a new user to the system
        /// </summary>
        [HttpPost("invite")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> InviteUser([FromBody] CreateInvitationDTO dto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                
                var invitation = new UserInvitation
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = dto.Email,
                    Role = dto.Role,
                    OrganizationId = dto.OrganizationId,
                    AuthType = dto.AuthType,
                    InvitedBy = currentUserId,
                    ExpiryDate = DateTime.UtcNow.AddDays(7)
                };

                var createdInvitation = await _authService.CreateUserInvitation(invitation);

                _logger.LogInformation("User invitation created for {Email} with role {Role} and auth type {AuthType} by {InvitedBy}",
                    dto.Email, dto.Role, dto.AuthType, currentUserId);

                // Send appropriate invitation email
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await SendInvitationEmail(createdInvitation);
                    }
                    catch (Exception emailEx)
                    {
                        _logger.LogError(emailEx, "Failed to send invitation email for {Email}", dto.Email);
                    }
                });

                return Ok(new UserInvitationDTO
                {
                    Id = createdInvitation.Id,
                    Email = createdInvitation.Email,
                    Role = createdInvitation.Role,
                    OrganizationId = createdInvitation.OrganizationId,
                    InvitedDate = createdInvitation.InvitedDate,
                    Status = createdInvitation.Status,
                    ExpiryDate = createdInvitation.ExpiryDate
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating invitation for {Email}", dto.Email);
                return BadRequest(new { message = ex.Message });
            }
        }

        /// <summary>
        /// Get all pending invitations
        /// </summary>
        [HttpGet("pending")]
        public async Task<IActionResult> GetPendingInvitations()
        {
            try
            {
                var invitations = await _authService.GetPendingInvitations();
                
                var invitationDtos = invitations.Select(i => new UserInvitationDTO
                {
                    Id = i.Id,
                    Email = i.Email,
                    Role = i.Role,
                    OrganizationId = i.OrganizationId,
                    InvitedDate = i.InvitedDate,
                    Status = i.Status,
                    ExpiryDate = i.ExpiryDate
                }).ToList();

                return Ok(invitationDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending invitations");
                return StatusCode(500, new { message = "Failed to get invitations" });
            }
        }

        /// <summary>
        /// Get all invitations (for admin)
        /// </summary>
        [HttpGet]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> GetAllInvitations()
        {
            try
            {
                var invitations = await _authService.GetAllUserInvitations();
                
                var invitationDtos = invitations.Select(i => new UserInvitationDTO
                {
                    Id = i.Id,
                    Email = i.Email,
                    Role = i.Role,
                    OrganizationId = i.OrganizationId,
                    InvitedDate = i.InvitedDate,
                    Status = i.Status,
                    ExpiryDate = i.ExpiryDate
                }).ToList();

                return Ok(invitationDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all invitations");
                return StatusCode(500, new { message = "Failed to get invitations" });
            }
        }

        /// <summary>
        /// Resend invitation email
        /// </summary>
        [HttpPost("{id}/resend")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> ResendInvitation(string id)
        {
            try
            {
                var invitation = await _authService.GetUserInvitationById(id);
                if (invitation == null)
                {
                    return NotFound(new { message = "Invitation not found" });
                }

                if (invitation.Status != "Pending")
                {
                    return BadRequest(new { message = "Can only resend pending invitations" });
                }

                // Send invitation email
                var emailSent = await SendInvitationEmail(invitation);

                if (emailSent)
                {
                    _logger.LogInformation("Invitation resent successfully for {Email}", invitation.Email);
                    return Ok(new { message = "Invitation resent successfully" });
                }
                else
                {
                    _logger.LogWarning("Invitation email failed to send for {Email}", invitation.Email);
                    return Ok(new { message = "Invitation updated but email failed to send" });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error resending invitation {InvitationId}", id);
                return StatusCode(500, new { message = "Failed to resend invitation" });
            }
        }

        /// <summary>
        /// Cancel/Delete invitation
        /// </summary>
        [HttpDelete("{id}")]
        [Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> CancelInvitation(string id)
        {
            try
            {
                var invitation = await _authService.GetUserInvitationById(id);
                if (invitation == null)
                {
                    return NotFound(new { message = "Invitation not found" });
                }

                if (invitation.Status != "Pending")
                {
                    return BadRequest(new { message = "Can only cancel pending invitations" });
                }

                await _authService.DeleteUserInvitation(id);
                
                _logger.LogInformation("Invitation cancelled for {Email}", invitation.Email);
                
                return Ok(new { message = "Invitation cancelled successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancelling invitation {InvitationId}", id);
                return StatusCode(500, new { message = "Failed to cancel invitation" });
            }
        }

        /// <summary>
        /// Send auth-type specific invitation email
        /// </summary>
        private async Task<bool> SendInvitationEmail(UserInvitation invitation)
        {
            try
            {
                var organizationId = invitation.OrganizationId ?? "SYSTEM";
                var subject = "You're Invited to Join Our System";
                var emailBody = invitation.AuthType == AuthenticationType.MICROSOFT 
                    ? CreateMicrosoftInvitationEmail(invitation)
                    : CreateLocalInvitationEmail(invitation);

                return await _emailService.SendCustomEmailAsync(
                    organizationId,
                    invitation.Email,
                    null,
                    subject,
                    emailBody
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending invitation email to {Email}", invitation.Email);
                return false;
            }
        }

        /// <summary>
        /// Create Microsoft invitation email
        /// </summary>
        private string CreateMicrosoftInvitationEmail(UserInvitation invitation)
        {
            var roleDisplayName = GetRoleDisplayName(invitation.Role);
            var loginUrl = GetMicrosoftLoginUrl();
            var expiryDate = invitation.ExpiryDate.ToString("MMMM dd, yyyy");

            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background-color: #0078d4; color: white; padding: 20px; text-align: center;'>
                            <h1 style='margin: 0;'>Payment Management System</h1>
                            <p style='margin: 10px 0 0 0;'>Microsoft Authentication Invitation</p>
                        </div>

                        <div style='padding: 30px; background-color: #f9f9f9;'>
                            <h2 style='color: #0078d4; margin-top: 0;'>You're Invited!</h2>
                            <p>You have been invited to join as a <strong>{roleDisplayName}</strong>.</p>
                            <p><strong>Authentication Method:</strong> Microsoft Account</p>

                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='{loginUrl}' 
                                   style='background-color: #0078d4; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;'>
                                    Sign in with Microsoft
                                </a>
                            </div>

                            <p><strong>Important:</strong> You must use your Microsoft account to access the system. Your account will be automatically set up when you sign in.</p>
                            <p><strong>Invitation expires:</strong> {expiryDate}</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        /// <summary>
        /// Create local account invitation email
        /// </summary>
        private string CreateLocalInvitationEmail(UserInvitation invitation)
        {
            var roleDisplayName = GetRoleDisplayName(invitation.Role);
            var setupUrl = GetLocalSetupUrl(invitation.Id);
            var expiryDate = invitation.ExpiryDate.ToString("MMMM dd, yyyy");

            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <div style='background-color: #045024; color: white; padding: 20px; text-align: center;'>
                            <h1 style='margin: 0;'>Payment Management System</h1>
                            <p style='margin: 10px 0 0 0;'>Local Account Invitation</p>
                        </div>

                        <div style='padding: 30px; background-color: #f9f9f9;'>
                            <h2 style='color: #045024; margin-top: 0;'>You're Invited!</h2>
                            <p>You have been invited to join as a <strong>{roleDisplayName}</strong>.</p>
                            <p><strong>Authentication Method:</strong> Local Account</p>

                            <div style='background-color: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px;'>
                                <h3 style='margin-top: 0;'>Your Login Credentials:</h3>
                                <p><strong>Email:</strong> {invitation.Email}</p>
                                <p><strong>Temporary Password:</strong> <code style='background: #fff; padding: 2px 6px;'>{invitation.TemporaryPassword}</code></p>
                            </div>

                            <div style='text-align: center; margin: 30px 0;'>
                                <a href='{setupUrl}' 
                                   style='background-color: #045024; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;'>
                                    Complete Account Setup
                                </a>
                            </div>

                            <p><strong>Next Steps:</strong></p>
                            <ol>
                                <li>Click the button above to access the setup page</li>
                                <li>Enter your temporary password</li>
                                <li>Create a new secure password</li>
                                <li>Provide your first and last name</li>
                                <li>Start using the system!</li>
                            </ol>

                            <p><strong>Invitation expires:</strong> {expiryDate}</p>
                        </div>
                    </div>
                </body>
                </html>";
        }

        private string GetMicrosoftLoginUrl()
        {
            return "http://localhost:3000/auth/microsoft";
        }

        private string GetLocalSetupUrl(string invitationId)
        {
            return $"http://localhost:3000/authlocal/setup/{invitationId}";
        }

        /// <summary>
        /// Get user-friendly role display name
        /// </summary>
        private string GetRoleDisplayName(string role)
        {
            return role switch
            {
                "ADMIN" => "Administrator",
                "JTB_ADMIN" => "Administrator",
                "FINANCE_OFFICER" => "Finance Officer",
                "SENIOR_FINANCE_OFFICER" => "Senior Finance Officer",
                "PAYER" => "Payer",
                _ => role
            };
        }

        /// <summary>
        /// Get current user ID from claims
        /// </summary>
        private string GetCurrentUserId()
        {
            return User.FindFirst("sub")?.Value ??
                   User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value ??
                   "system";
        }
    }
}

