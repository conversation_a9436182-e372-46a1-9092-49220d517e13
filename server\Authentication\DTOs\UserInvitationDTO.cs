namespace Final_E_Receipt.Authentication.DTOs
{
    public class UserInvitationDTO
    {
        public string Id { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public string OrganizationId { get; set; }
        public string OrganizationName { get; set; } // For display purposes
        public DateTime InvitedDate { get; set; }
        public string Status { get; set; }
        public DateTime ExpiryDate { get; set; }
        public DateTime? AcceptedDate { get; set; }
        public string InvitedBy { get; set; }
        public string InvitedByName { get; set; } // For display purposes
    }
}