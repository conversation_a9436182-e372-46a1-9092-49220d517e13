namespace Final_E_Receipt.Authentication.Models
{
    public class UserInvitation
    {
        public string Id { get; set; }
        public string Email { get; set; }
        public string Role { get; set; }
        public string OrganizationId { get; set; }
        public AuthenticationType AuthType { get; set; } // MICROSOFT or LOCAL
        public string? TemporaryPassword { get; set; } // Only for LOCAL auth
        public DateTime InvitedDate { get; set; }
        public string Status { get; set; } // Pending, Accepted, Cancelled, Expired
        public string InvitedBy { get; set; }
        public string InvitedByName { get; set; } // Name of the person who invited
        public DateTime ExpiryDate { get; set; }
        public DateTime? AcceptedDate { get; set; }
    }

    public enum AuthenticationType
    {
        MICROSOFT,
        LOCAL
    }
}
