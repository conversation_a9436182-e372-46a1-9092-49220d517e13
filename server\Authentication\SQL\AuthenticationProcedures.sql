-- Create Users Table


-- Create UserInvitations Table
CREATE TABLE UserInvitations (
    Id NVARCHAR(50) PRIMARY KEY,
    Email NVARCHAR(255) NOT NULL,
    Role NVARCHAR(50) NOT NULL,
    OrganizationId NVARCHAR(50) NOT NULL,
    InvitedDate <PERSON><PERSON>ETIME NOT NULL DEFAULT GETDATE(),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending',
    InvitedBy NVARCHAR(50) NOT NULL,
    InvitedByName NVARCHAR(255) NOT NULL,
    ExpiryDate DATETIME NOT NULL,
    AcceptedDate DATETIME NULL
);

-- Add missing columns if they don't exist (for existing databases)
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserInvitations' AND COLUMN_NAME = 'InvitedByName')
BEGIN
    ALTER TABLE UserInvitations ADD InvitedByName NVARCHAR(255) NOT NULL DEFAULT '';
END;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserInvitations' AND COLUMN_NAME = 'AuthType')
BEGIN
    ALTER TABLE UserInvitations ADD AuthType NVARCHAR(20) NOT NULL DEFAULT 'MICROSOFT';
END;

IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'UserInvitations' AND COLUMN_NAME = 'TemporaryPassword')
BEGIN
    ALTER TABLE UserInvitations ADD TemporaryPassword NVARCHAR(50) NULL;
END;

-- Get User By Azure AD Object ID
CREATE PROCEDURE GetUserByAzureAdObjectId
    @AzureAdObjectId NVARCHAR(100)
AS
BEGIN
    SELECT 
        Id, FirstName, LastName, Email, PhoneNumber, Role, 
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId
    FROM Users
    WHERE AzureAdObjectId = @AzureAdObjectId
END;

-- Get User By Email
CREATE PROCEDURE GetUserByEmail
    @Email NVARCHAR(255)
AS
BEGIN
    SELECT
        Id, FirstName, LastName, Email, PhoneNumber, Role,
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId
    FROM Users
    WHERE Email = @Email
END;

-- Get Pending Invitation By Email
CREATE PROCEDURE GetPendingInvitationByEmail
    @Email NVARCHAR(255)
AS
BEGIN
    SELECT
        Id, Email, Role, OrganizationId, InvitedDate, Status, InvitedBy, InvitedByName, ExpiryDate
    FROM UserInvitations
    WHERE Email = @Email
    AND Status = 'Pending'
    AND ExpiryDate > GETDATE()
END;

-- Mark Invitation as Accepted
CREATE PROCEDURE MarkInvitationAccepted
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE UserInvitations
    SET Status = 'Accepted'
    WHERE Id = @Id
END;

-- Get Pending Invitations
CREATE PROCEDURE GetPendingInvitations
AS
BEGIN
    SELECT
        Id, Email, Role, OrganizationId, InvitedDate, Status, InvitedBy, ExpiryDate
    FROM UserInvitations
    WHERE Status = 'Pending'
    AND ExpiryDate > GETDATE()
    ORDER BY InvitedDate DESC
END;

-- Get User Invitation By ID
CREATE PROCEDURE GetUserInvitationById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT
        Id, Email, Role, OrganizationId, InvitedDate, Status, InvitedBy, ExpiryDate
    FROM UserInvitations
    WHERE Id = @Id
END;

-- Cancel User Invitation (Soft Delete)
CREATE PROCEDURE CancelUserInvitation
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE UserInvitations
    SET Status = 'Cancelled'
    WHERE Id = @Id
    AND Status = 'Pending'  -- Only cancel pending invitations

    -- Return the updated invitation
    SELECT
        Id, Email, Role, OrganizationId, InvitedDate, Status, InvitedBy, ExpiryDate
    FROM UserInvitations
    WHERE Id = @Id
END;

-- Create User
CREATE PROCEDURE CreateUser
    @Id NVARCHAR(50),
    @FirstName NVARCHAR(100),
    @LastName NVARCHAR(100),
    @Email NVARCHAR(255),
    @PhoneNumber NVARCHAR(20),
    @Role NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @AzureAdObjectId NVARCHAR(100)
AS
BEGIN
    INSERT INTO Users (
        Id, FirstName, LastName, Email, PhoneNumber, 
        Role, OrganizationId, IsActive, AzureAdObjectId
    )
    VALUES (
        @Id, @FirstName, @LastName, @Email, @PhoneNumber, 
        @Role, @OrganizationId, 1, @AzureAdObjectId
    )
    
    SELECT 
        Id, FirstName, LastName, Email, PhoneNumber, Role, 
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId
    FROM Users
    WHERE Id = @Id
END;

-- Update User Last Login
CREATE PROCEDURE UpdateUserLastLogin
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE Users
    SET LastLogin = GETDATE()
    WHERE Id = @Id
    
    SELECT 
        Id, FirstName, LastName, Email, PhoneNumber, Role, 
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId
    FROM Users
    WHERE Id = @Id
END;

-- Drop old CreateUserInvitation procedure (replaced by newer version below)
DROP PROCEDURE IF EXISTS CreateUserInvitation;

-- Get User Invitation By Email
CREATE PROCEDURE GetUserInvitationByEmail
    @Email NVARCHAR(255)
AS
BEGIN
    SELECT 
        Id, Email, Role, InvitedDate, Status, InvitedBy, ExpiryDate
    FROM UserInvitations
    WHERE Email = @Email AND Status = 'Pending'
END;

-- Get All User Invitations
CREATE PROCEDURE GetAllUserInvitations
AS
BEGIN
    SELECT
        Id, Email, Role, OrganizationId, InvitedDate, Status, InvitedBy, ExpiryDate
    FROM UserInvitations
    ORDER BY InvitedDate DESC
END;

-- Cancel User Invitation
CREATE PROCEDURE CancelUserInvitation
    @Id NVARCHAR(50)
AS
BEGIN
    UPDATE UserInvitations
    SET Status = 'Cancelled'
    WHERE Id = @Id
    
    SELECT 
        Id, Email, Role, InvitedDate, Status, InvitedBy, ExpiryDate
    FROM UserInvitations
    WHERE Id = @Id
END;

-- Accept User Invitation
CREATE PROCEDURE AcceptUserInvitation
    @Email NVARCHAR(255),
    @AzureAdObjectId NVARCHAR(100),
    @FirstName NVARCHAR(100),
    @LastName NVARCHAR(100)
AS
BEGIN
    DECLARE @UserId NVARCHAR(50) = NEWID()
    DECLARE @Role NVARCHAR(50)
    DECLARE @InvitationId NVARCHAR(50)
    
    -- Get invitation details
    SELECT 
        @Role = Role,
        @InvitationId = Id
    FROM UserInvitations
    WHERE Email = @Email AND Status = 'Pending'
    
    -- Create user
    INSERT INTO Users (
        Id, FirstName, LastName, Email, Role, IsActive, AzureAdObjectId
    )
    VALUES (
        @UserId, @FirstName, @LastName, @Email, @Role, 1, @AzureAdObjectId
    )
    
    -- Update invitation status
    UPDATE UserInvitations
    SET Status = 'Accepted'
    WHERE Id = @InvitationId
    
    -- Return user details
    SELECT 
        Id, FirstName, LastName, Email, PhoneNumber, Role, 
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId
    FROM Users
    WHERE Id = @UserId
END;

-- Update UserInvitations table
ALTER TABLE UserInvitations 
ADD AuthType NVARCHAR(20) NOT NULL DEFAULT 'MICROSOFT',
    TemporaryPassword NVARCHAR(50) NULL;

-- Update Users table
ALTER TABLE Users 
ADD AuthType NVARCHAR(20) NOT NULL DEFAULT 'MICROSOFT',
    PasswordHash NVARCHAR(255) NULL,
    MustResetPassword BIT NOT NULL DEFAULT 0;

-- Create User Invitation with Auth Type
CREATE OR ALTER PROCEDURE CreateUserInvitation
    @Id NVARCHAR(50),
    @Email NVARCHAR(255),
    @Role NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @AuthType NVARCHAR(20),
    @TemporaryPassword NVARCHAR(50) = NULL,
    @InvitedBy NVARCHAR(50),
    @InvitedByName NVARCHAR(255),
    @ExpiryDays INT = 7
AS
BEGIN
    INSERT INTO UserInvitations (
        Id, Email, Role, OrganizationId, AuthType, TemporaryPassword,
        InvitedDate, Status, InvitedBy, InvitedByName, ExpiryDate
    )
    VALUES (
        @Id, @Email, @Role, @OrganizationId, @AuthType, @TemporaryPassword,
        GETDATE(), 'Pending', @InvitedBy, @InvitedByName, DATEADD(DAY, @ExpiryDays, GETDATE())
    )

    SELECT * FROM UserInvitations WHERE Id = @Id
END;

-- Create Local User
CREATE OR ALTER PROCEDURE CreateLocalUser
    @Id NVARCHAR(50),
    @FirstName NVARCHAR(100),
    @LastName NVARCHAR(100),
    @Email NVARCHAR(255),
    @PasswordHash NVARCHAR(255),
    @AuthType NVARCHAR(20),
    @Role NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @IsActive BIT,
    @MustResetPassword BIT
AS
BEGIN
    INSERT INTO Users (
        Id, FirstName, LastName, Email, PasswordHash, AuthType,
        Role, OrganizationId, IsActive, MustResetPassword, CreatedAt
    )
    VALUES (
        @Id, @FirstName, @LastName, @Email, @PasswordHash, @AuthType,
        @Role, @OrganizationId, @IsActive, @MustResetPassword, GETDATE()
    )

    SELECT * FROM Users WHERE Id = @Id
END;

-- Create Microsoft User
CREATE OR ALTER PROCEDURE CreateMicrosoftUser
    @Id NVARCHAR(50),
    @FirstName NVARCHAR(100),
    @LastName NVARCHAR(100),
    @Email NVARCHAR(255),
    @AzureAdObjectId NVARCHAR(100),
    @AuthType NVARCHAR(20),
    @Role NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @IsActive BIT,
    @MustResetPassword BIT
AS
BEGIN
    INSERT INTO Users (
        Id, FirstName, LastName, Email, AzureAdObjectId, AuthType,
        Role, OrganizationId, IsActive, MustResetPassword, CreatedAt
    )
    VALUES (
        @Id, @FirstName, @LastName, @Email, @AzureAdObjectId, @AuthType,
        @Role, @OrganizationId, @IsActive, @MustResetPassword, GETDATE()
    )

    SELECT * FROM Users WHERE Id = @Id
END;

-- Get Local User By Email
CREATE OR ALTER PROCEDURE GetLocalUserByEmail
    @Email NVARCHAR(255)
AS
BEGIN
    SELECT * FROM Users
    WHERE Email = @Email AND AuthType = 'LOCAL'
END;

-- Get All Users
CREATE OR ALTER PROCEDURE GetAllUsers
AS
BEGIN
    SELECT
        Id, FirstName, LastName, Email, PhoneNumber, Role,
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId, AuthType
    FROM Users
    ORDER BY CreatedAt DESC
END;

-- Update User
CREATE OR ALTER PROCEDURE UpdateUser
    @Id NVARCHAR(50),
    @FirstName NVARCHAR(100),
    @LastName NVARCHAR(100),
    @Role NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @IsActive BIT,
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    UPDATE Users
    SET
        FirstName = @FirstName,
        LastName = @LastName,
        Role = @Role,
        OrganizationId = @OrganizationId,
        IsActive = @IsActive,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id;

    SELECT
        Id, FirstName, LastName, Email, PhoneNumber, Role,
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId, AuthType
    FROM Users
    WHERE Id = @Id
END;

-- Get Users By Organization
CREATE OR ALTER PROCEDURE GetUsersByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT
        Id, FirstName, LastName, Email, PhoneNumber, Role,
        OrganizationId, IsActive, CreatedAt, LastLogin, AzureAdObjectId, AuthType
    FROM Users
    WHERE OrganizationId = @OrganizationId
    ORDER BY CreatedAt DESC
END;

