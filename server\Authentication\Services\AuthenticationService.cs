using System.Data;
using System.Security.Claims;
using Final_E_Receipt.Authentication.Models;
using Final_E_Receipt.Authentication.DTOs;
using Final_E_Receipt.Services;
using Final_E_Receipt.Common.Services;
using Final_E_Receipt.Common.Models;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.Text;
using System.IdentityModel.Tokens.Jwt;
using Microsoft.Extensions.Configuration;

namespace Final_E_Receipt.Authentication.Services
{
    public class AuthenticationService
    {
        private readonly IDatabaseService _dbService;
        private readonly ILogger<AuthenticationService> _logger;
        private readonly IConfiguration _configuration;
        private readonly AuditService _auditService;
        private readonly string _jwtSecret;
        private readonly string _jwtIssuer;
        private readonly string _jwtAudience;

        public AuthenticationService(IDatabaseService dbService, ILogger<AuthenticationService> logger, IConfiguration configuration, AuditService auditService)
        {
            _dbService = dbService;
            _logger = logger;
            _configuration = configuration;
            _auditService = auditService;
            _jwtSecret = _configuration["JwtSettings:SecretKey"];
            _jwtIssuer = _configuration["JwtSettings:Issuer"];
            _jwtAudience = _configuration["JwtSettings:Audience"];
        }

        /// <summary>
        /// Process Microsoft login and create/update user based on invitation
        /// </summary>
        public async Task<User> ProcessMicrosoftLogin(ClaimsPrincipal principal)
        {
            try
            {
                var objectId = principal.FindFirst("oid")?.Value ??
                              principal.FindFirst("http://schemas.microsoft.com/identity/claims/objectidentifier")?.Value;
                var email = principal.FindFirst("preferred_username")?.Value ??
                           principal.FindFirst("upn")?.Value ??
                           principal.FindFirst(ClaimTypes.Email)?.Value;
                var firstName = principal.FindFirst("given_name")?.Value ??
                               principal.FindFirst(ClaimTypes.GivenName)?.Value;
                var lastName = principal.FindFirst("family_name")?.Value ??
                              principal.FindFirst(ClaimTypes.Surname)?.Value;

                if (string.IsNullOrEmpty(objectId) || string.IsNullOrEmpty(email))
                    throw new UnauthorizedAccessException("Missing required user information from Microsoft");

                // Check if user already exists
                var existingUser = await GetUserByAzureAdObjectId(objectId);
                if (existingUser != null)
                {
                    await _auditService.LogLoginAsync(existingUser.Id, existingUser.Email, $"{existingUser.FirstName} {existingUser.LastName}", true);
                    return await UpdateUserLastLogin(existingUser.Id);
                }

                // New user - check for pending MICROSOFT invitation
                var invitation = await GetPendingInvitation(email);
                if (invitation == null)
                    throw new UnauthorizedAccessException("No invitation found for this email address");

                if (invitation.AuthType != AuthenticationType.MICROSOFT)
                    throw new UnauthorizedAccessException("This email was invited for local authentication only");

                // Create new Microsoft user
                var newUser = await CreateMicrosoftUserFromInvitation(objectId, email, firstName, lastName, invitation);
                return newUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Microsoft login");
                throw;
            }
        }

        public async Task<User> GetUserByAzureAdObjectId(string azureAdObjectId)
        {
            return await _dbService.QueryFirstOrDefaultAsync<User>(
                "GetUserByAzureAdObjectId",
                new { AzureAdObjectId = azureAdObjectId });
        }

        /// <summary>
        /// Get user by email (any auth type)
        /// </summary>
        public async Task<User> GetUserByEmail(string email)
        {
            return await _dbService.QueryFirstOrDefaultAsync<User>(
                "GetUserByEmail",
                new { Email = email });
        }

        public async Task<User> UpdateUserLastLogin(string userId)
        {
            return await _dbService.QueryFirstOrDefaultAsync<User>(
                "UpdateUserLastLogin",
                new { Id = userId });
        }

        /// <summary>
        /// Create user invitation with auth type
        /// </summary>
        public async Task<UserInvitation> CreateUserInvitation(UserInvitation invitation)
        {
            // Generate temporary password for LOCAL auth
            if (invitation.AuthType == AuthenticationType.LOCAL)
            {
                invitation.TemporaryPassword = GenerateTemporaryPassword();
            }

            var result = await _dbService.QueryFirstOrDefaultAsync<UserInvitation>(
                "CreateUserInvitation",
                new
                {
                    Id = invitation.Id,
                    Email = invitation.Email,
                    Role = invitation.Role,
                    OrganizationId = invitation.OrganizationId,
                    AuthType = invitation.AuthType.ToString(),
                    TemporaryPassword = invitation.TemporaryPassword,
                    InvitedBy = invitation.InvitedBy,
                    InvitedByName = invitation.InvitedByName,
                    ExpiryDays = 7
                });

            // Log the invitation creation
            await _auditService.LogAsync(
                AuditActions.USER_INVITED,
                EntityTypes.USER_INVITATION,
                invitation.Id,
                null,
                new { invitation.Email, invitation.Role, invitation.OrganizationId, invitation.AuthType },
                $"User invited with role {invitation.Role} by {invitation.InvitedByName}"
            );

            return result;
        }

        public async Task<List<UserInvitation>> GetAllUserInvitations()
        {
            var result = await _dbService.QueryAsync<UserInvitation>("GetAllUserInvitations");
            return result.ToList();
        }

        public async Task<UserInvitation> CancelUserInvitation(string invitationId)
        {
            return await _dbService.QueryFirstOrDefaultAsync<UserInvitation>(
                "CancelUserInvitation",
                new { Id = invitationId });
        }

        public async Task<User> AcceptUserInvitation(string email, string azureAdObjectId, string firstName, string lastName)
        {
            return await _dbService.QueryFirstOrDefaultAsync<User>(
                "AcceptUserInvitation",
                new
                {
                    Email = email,
                    AzureAdObjectId = azureAdObjectId,
                    FirstName = firstName,
                    LastName = lastName
                });
        }

        public async Task<User> CreateInitialAdminUser(string azureAdObjectId, string email, string firstName, string lastName)
        {
            // Check if any admin users exist
            var existingAdmins = await GetUsersByRole("JTB_ADMIN");
            if (existingAdmins.Count > 0)
            {
                throw new InvalidOperationException("Admin users already exist. Cannot create initial admin.");
            }

            return await _dbService.QueryFirstOrDefaultAsync<User>(
                "CreateUser",
                new
                {
                    Id = Guid.NewGuid().ToString(),
                    FirstName = firstName,
                    LastName = lastName,
                    Email = email,
                    PhoneNumber = (string)null,
                    Role = "JTB_ADMIN",
                    OrganizationId = (string)null,
                    AzureAdObjectId = azureAdObjectId
                });
        }

        public async Task<List<User>> GetUsersByRole(string role)
        {
            var result = await _dbService.QueryAsync<User>(
                "GetUsersByRole",
                new { Role = role });
            return result.ToList();
        }

        /// <summary>
        /// Get pending invitation by email
        /// </summary>
        public async Task<UserInvitation> GetPendingInvitation(string email)
        {
            return await _dbService.QueryFirstOrDefaultAsync<UserInvitation>(
                "GetPendingInvitationByEmail",
                new { Email = email });
        }

        /// <summary>
        /// Create user from invitation
        /// </summary>
        public async Task<User> CreateUserFromInvitation(string objectId, string email, string firstName, string lastName, UserInvitation invitation)
        {
            var newUser = await _dbService.QueryFirstOrDefaultAsync<User>(
                "CreateUser",
                new
                {
                    Id = Guid.NewGuid().ToString(),
                    FirstName = firstName,
                    LastName = lastName,
                    Email = email,
                    Role = invitation.Role,
                    OrganizationId = invitation.OrganizationId,
                    AzureAdObjectId = objectId,
                    IsActive = true
                });

            if (newUser == null)
                throw new InvalidOperationException("Failed to create user");

            // Mark invitation as accepted
            await MarkInvitationAccepted(invitation.Id);

            return newUser;
        }

        /// <summary>
        /// Mark invitation as accepted
        /// </summary>
        private async Task MarkInvitationAccepted(string invitationId)
        {
            await _dbService.ExecuteAsync(
                "MarkInvitationAccepted",
                new { Id = invitationId });
        }

        public async Task<List<UserInvitation>> GetPendingInvitations()
        {
            var result = await _dbService.QueryAsync<UserInvitation>("GetPendingInvitations");
            return result.ToList();
        }

        public async Task<UserInvitation> GetUserInvitationById(string id)
        {
            return await _dbService.QueryFirstOrDefaultAsync<UserInvitation>(
                "GetUserInvitationById",
                new { Id = id });
        }

        public async Task DeleteUserInvitation(string invitationId)
        {
            await _dbService.ExecuteAsync(
                "CancelUserInvitation",
                new { Id = invitationId });

            _logger.LogInformation("User invitation {InvitationId} marked as cancelled", invitationId);
        }

        /// <summary>
        /// Complete local account setup (password reset + name)
        /// </summary>
        public async Task<User> CompleteLocalAccountSetup(string email, string tempPassword, string newPassword, string firstName, string lastName)
        {
            await using var connection = _dbService.GetConnection();
            await connection.OpenAsync();

            await using var transaction = await connection.BeginTransactionAsync();

            try
            {
                // Validate invitation WITHIN transaction
                var invitation = await _dbService.QueryFirstOrDefaultAsync<UserInvitation>(
                    connection,
                    "GetPendingInvitationByEmail",
                    new { Email = email },
                    transaction);

                if (invitation == null)
                    throw new UnauthorizedAccessException("No invitation found");

                if (invitation.AuthType != AuthenticationType.LOCAL)
                    throw new UnauthorizedAccessException("This email was invited for Microsoft authentication");

                if (invitation.TemporaryPassword != tempPassword)
                    throw new UnauthorizedAccessException("Invalid temporary password");

                if (invitation.ExpiryDate < DateTime.UtcNow)
                    throw new UnauthorizedAccessException("Invitation has expired");

                var passwordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);

                // Create user
                var newUser = await _dbService.QueryFirstOrDefaultAsync<User>(
                    connection,
                    "CreateLocalUser",
                    new
                    {
                        Id = Guid.NewGuid().ToString(),
                        FirstName = firstName,
                        LastName = lastName,
                        Email = email,
                        PasswordHash = passwordHash,
                        AuthType = "LOCAL",
                        Role = invitation.Role,
                        OrganizationId = invitation.OrganizationId,
                        IsActive = true,
                        MustResetPassword = false
                    },
                    transaction);

                if (newUser == null)
                    throw new InvalidOperationException($"User creation failed for {email}");

                // Mark invitation as accepted
                await _dbService.ExecuteAsync(
                    connection,
                    "MarkInvitationAccepted",
                    new { Id = invitation.Id },
                    transaction);

                await transaction.CommitAsync();
                return newUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Account setup failed for {Email}", email);
                await transaction.RollbackAsync();
                throw;
            }
        }
        /// <summary>
        /// Validate local login
        /// </summary>
        public async Task<User> ValidateLocalLogin(string email, string password)
        {
            var user = await _dbService.QueryFirstOrDefaultAsync<User>(
                "GetLocalUserByEmail",
                new { Email = email });

            if (user == null || user.AuthType != AuthenticationType.LOCAL)
                return null;

            if (!BCrypt.Net.BCrypt.Verify(password, user.PasswordHash))
                return null;

            return await UpdateUserLastLogin(user.Id);
        }

        /// <summary>
        /// Create Microsoft user from invitation
        /// </summary>
        private async Task<User> CreateMicrosoftUserFromInvitation(string objectId, string email, string firstName, string lastName, UserInvitation invitation)
        {
            var newUser = await _dbService.QueryFirstOrDefaultAsync<User>(
                "CreateMicrosoftUser",
                new
                {
                    Id = Guid.NewGuid().ToString(),
                    FirstName = firstName,
                    LastName = lastName,
                    Email = email,
                    AzureAdObjectId = objectId,
                    AuthType = "MICROSOFT",
                    Role = invitation.Role,
                    OrganizationId = invitation.OrganizationId,
                    IsActive = true,
                    MustResetPassword = false
                });

            if (newUser == null)
                throw new InvalidOperationException("Failed to create user");

            // Log user creation and invitation acceptance
            await _auditService.LogAsync(
                AuditActions.USER_CREATED,
                EntityTypes.USER,
                newUser.Id,
                null,
                new { newUser.FirstName, newUser.LastName, newUser.Email, newUser.Role, newUser.OrganizationId },
                $"Microsoft user created from invitation"
            );

            await _auditService.LogAsync(
                AuditActions.USER_INVITATION_ACCEPTED,
                EntityTypes.USER_INVITATION,
                invitation.Id,
                null,
                new { invitation.Email, AcceptedBy = $"{firstName} {lastName}" },
                "User invitation accepted via Microsoft login"
            );

            await MarkInvitationAccepted(invitation.Id);
            return newUser;
        }

        /// <summary>
        /// Generate temporary password
        /// </summary>
        private string GenerateTemporaryPassword()
        {
            const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, 12)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }

        public string GenerateInternalJwtToken(User user)
        {
            var jwtSettings = _configuration.GetSection("JwtSettings");
            var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]!));
            var creds = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

            var claims = new List<Claim>
            {
                new Claim("sub", user.Id),
                new Claim("email", user.Email),
                new Claim("role", user.Role),
                new Claim("organizationId", user.OrganizationId ?? "")
            };

            var token = new JwtSecurityToken(
                issuer: jwtSettings["Issuer"],
                audience: jwtSettings["Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddHours(8),
                signingCredentials: creds);

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        public async Task<User> CreateAdminUserAsync(string firstName, string lastName, string email, string password)
        {
            try
            {
                // Check if user with this email already exists
                var existingUser = await GetUserByEmail(email);
                if (existingUser != null)
                {
                    throw new InvalidOperationException("User with this email already exists.");
                }

                // Hash the password
                var passwordHash = BCrypt.Net.BCrypt.HashPassword(password);

                // Generate a unique identifier for local users to avoid NULL constraint issues
                var localUserAzureId = $"LOCAL_{Guid.NewGuid()}";

                // Create admin user using your existing database pattern
                var adminUser = await _dbService.QueryFirstOrDefaultAsync<User>(
                    "CreateLocalUser",
                    new
                    {
                        Id = Guid.NewGuid().ToString(),
                        FirstName = firstName,
                        LastName = lastName,
                        Email = email,
                        PasswordHash = passwordHash,
                        AuthType = "LOCAL", // Admin uses local authentication
                        Role = "JTB_ADMIN",
                        OrganizationId = (string)null, // Admin doesn't belong to specific org
                        IsActive = true,
                        MustResetPassword = false, // Admin sets their own password
                        AzureAdObjectId = localUserAzureId // Use unique identifier instead of NULL
                    });

                if (adminUser == null)
                {
                    throw new InvalidOperationException("Failed to create admin user");
                }

                _logger.LogInformation("Admin user created successfully with email: {Email}", email);
                return adminUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating admin user with email: {Email}", email);
                throw;
            }
        }

        // Alternative method if you want to create initial admin without restrictions
        public async Task<User> CreateInitialAdminUserAsync(string firstName, string lastName, string email, string password)
        {
            try
            {
                // Hash the password
                var passwordHash = BCrypt.Net.BCrypt.HashPassword(password);

                // Create the initial admin user
                var adminUser = await _dbService.QueryFirstOrDefaultAsync<User>(
                    "CreateLocalUser",
                    new
                    {
                        Id = Guid.NewGuid().ToString(),
                        FirstName = firstName,
                        LastName = lastName,
                        Email = email,
                        PasswordHash = passwordHash,
                        AuthType = "LOCAL",
                        Role = "JTB_ADMIN",
                        OrganizationId = (string)null,
                        IsActive = true,
                        MustResetPassword = false,
                        AzureAdObjectId = (string)null

                    });

                if (adminUser == null)
                {
                    throw new InvalidOperationException("Failed to create initial admin user");
                }

                _logger.LogInformation("Initial admin user created successfully with email: {Email}", email);
                return adminUser;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating initial admin user with email: {Email}", email);
                throw;
            }
        }

        /// <summary>
        /// Get all users in the system
        /// </summary>
        public async Task<List<User>> GetAllUsers()
        {
            var users = await _dbService.QueryAsync<User>("GetAllUsers", new { });
            return users.ToList();
        }

        /// <summary>
        /// Get user by ID
        /// </summary>
        public async Task<User> GetUserById(string userId)
        {
            return await _dbService.QueryFirstOrDefaultAsync<User>("GetUserById", new { Id = userId });
        }

        /// <summary>
        /// Update user details
        /// </summary>
        public async Task<User> UpdateUser(User user, string updatedBy)
        {
            // Get the current user state for audit logging
            var oldUser = await GetUserById(user.Id);

            var parameters = new
            {
                Id = user.Id,
                FirstName = user.FirstName,
                LastName = user.LastName,
                Role = user.Role,
                OrganizationId = user.OrganizationId,
                IsActive = user.IsActive,
                UpdatedBy = updatedBy
            };

            var updatedUser = await _dbService.QueryFirstOrDefaultAsync<User>("UpdateUser", parameters);

            // Log the user update
            if (updatedUser != null && oldUser != null)
            {
                await _auditService.LogAsync(
                    AuditActions.USER_UPDATED,
                    EntityTypes.USER,
                    user.Id,
                    new { oldUser.FirstName, oldUser.LastName, oldUser.Role, oldUser.OrganizationId, oldUser.IsActive },
                    new { updatedUser.FirstName, updatedUser.LastName, updatedUser.Role, updatedUser.OrganizationId, updatedUser.IsActive },
                    $"User updated by {updatedBy}"
                );
            }

            return updatedUser;
        }

        /// <summary>
        /// Get users by organization
        /// </summary>
        public async Task<List<User>> GetUsersByOrganization(string organizationId)
        {
            var users = await _dbService.QueryAsync<User>("GetUsersByOrganization", new { OrganizationId = organizationId });
            return users.ToList();
        }
    }
}









