using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Notifications.DTOs;
using Final_E_Receipt.Notifications.Models;
using Final_E_Receipt.Notifications.Services;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Final_E_Receipt.Notifications.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class EmailConfigurationController : ControllerBase
    {
        private readonly EmailConfigurationService _emailConfigService;

        public EmailConfigurationController(EmailConfigurationService emailConfigService)
        {
            _emailConfigService = emailConfigService;
        }

        [HttpPost]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreateEmailConfiguration([FromBody] CreateEmailConfigurationDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var config = new EmailConfiguration
            {
                OrganizationId = dto.OrganizationId,
                SmtpServer = dto.SmtpServer,
                Port = dto.Port,
                Username = dto.Username,
                Password = dto.Password,
                EnableSsl = dto.EnableSsl,
                IsDefault = dto.IsDefault,
                SenderName = dto.SenderName,
                SenderEmail = dto.SenderEmail,
                CreatedBy = userId
            };

            var createdConfig = await _emailConfigService.CreateEmailConfiguration(config);
            
            if (createdConfig == null)
                return BadRequest(new { message = "Failed to create email configuration" });

            return Ok(createdConfig);
        }

        [HttpGet("organization/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailConfigurationsByOrganization(string organizationId)
        {
            var configs = await _emailConfigService.GetEmailConfigurationsByOrganization(organizationId);
            return Ok(configs);
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetEmailConfigurationById(string id)
        {
            var config = await _emailConfigService.GetEmailConfigurationById(id);
            
            if (config == null)
                return NotFound(new { message = "Email configuration not found" });

            return Ok(config);
        }

        [HttpGet("default/{organizationId}")]
        [Authorize(Roles = "ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetDefaultEmailConfiguration(string organizationId)
        {
            var config = await _emailConfigService.GetDefaultEmailConfiguration(organizationId);
            
            if (config == null)
                return NotFound(new { message = "Default email configuration not found" });

            return Ok(config);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdateEmailConfiguration(string id, [FromBody] UpdateEmailConfigurationDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var existingConfig = await _emailConfigService.GetEmailConfigurationById(id);
            if (existingConfig == null)
                return NotFound(new { message = "Email configuration not found" });

            existingConfig.SmtpServer = dto.SmtpServer;
            existingConfig.Port = dto.Port;
            existingConfig.Username = dto.Username;
            existingConfig.Password = dto.Password;
            existingConfig.EnableSsl = dto.EnableSsl;
            existingConfig.IsDefault = dto.IsDefault;
            existingConfig.SenderName = dto.SenderName;
            existingConfig.SenderEmail = dto.SenderEmail;
            existingConfig.UpdatedBy = userId;

            var updatedConfig = await _emailConfigService.UpdateEmailConfiguration(existingConfig);
            
            if (updatedConfig == null)
                return BadRequest(new { message = "Failed to update email configuration" });

            return Ok(updatedConfig);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeleteEmailConfiguration(string id)
        {
            var existingConfig = await _emailConfigService.GetEmailConfigurationById(id);
            if (existingConfig == null)
                return NotFound(new { message = "Email configuration not found" });

            var result = await _emailConfigService.DeleteEmailConfiguration(id);
            
            if (!result)
                return BadRequest(new { message = "Failed to delete email configuration" });

            return Ok(new { message = "Email configuration deleted successfully" });
        }
    }
}