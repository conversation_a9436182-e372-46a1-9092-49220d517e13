using System;

namespace Final_E_Receipt.Notifications.Models
{
    public class EmailConfiguration
    {
        public string Id { get; set; }
        public string OrganizationId { get; set; }
        public string SmtpServer { get; set; }
        public int Port { get; set; }
        public string Username { get; set; }
        public string Password { get; set; }
        public bool EnableSsl { get; set; }
        public bool IsDefault { get; set; }
        public string SenderName { get; set; }
        public string SenderEmail { get; set; }
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }
    }
}