-- Email Templates Table
CREATE TABLE EmailTemplates (
    Id NVARCHAR(50) PRIMARY KEY,
    OrganizationId NVARCHAR(50) NOT NULL,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(500),
    Subject NVARCHAR(255) NOT NULL,
    BodyContent NVARCHAR(MAX) NOT NULL,
    TemplateContent NVARCHAR(MAX),
    Type NVARCHAR(50) NOT NULL, -- RECEIPT_NOTIFICATION, PAYMENT_REMINDER, etc.
    IsDefault BIT NOT NULL DEFAULT 0,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL
);

-- Email Configurations Table
CREATE TABLE EmailConfigurations (
    Id NVARCHAR(50) PRIMARY KEY,
    OrganizationId NVARCHAR(50) NOT NULL,
    SmtpServer NVARCHAR(255) NOT NULL,
    Port INT NOT NULL,
    Username NVARCHAR(255) NOT NULL,
    Password NVARCHAR(255) NOT NULL,
    EnableSsl BIT NOT NULL DEFAULT 1,
    IsDefault BIT NOT NULL DEFAULT 0,
    SenderName NVARCHAR(100) NOT NULL,
    SenderEmail NVARCHAR(255) NOT NULL,
    CreatedAt DATETIME NOT NULL DEFAULT GETDATE(),
    CreatedBy NVARCHAR(50),
    UpdatedAt DATETIME NULL,
    UpdatedBy NVARCHAR(50) NULL
);

-- Create Email Template
CREATE PROCEDURE CreateEmailTemplate
    @Id NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500),
    @Subject NVARCHAR(255),
    @BodyContent NVARCHAR(MAX),
    @Type NVARCHAR(50),
    @IsDefault BIT,
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    -- If this is set as default, unset any existing default for this type
    IF @IsDefault = 1
    BEGIN
        UPDATE EmailTemplates
        SET IsDefault = 0
        WHERE OrganizationId = @OrganizationId AND Type = @Type
    END

    INSERT INTO EmailTemplates (
        Id, OrganizationId, Name, Description, Subject, 
        BodyContent, Type, IsDefault, CreatedBy
    )
    VALUES (
        @Id, @OrganizationId, @Name, @Description, @Subject, 
        @BodyContent, @Type, @IsDefault, @CreatedBy
    )
    
    SELECT * FROM EmailTemplates WHERE Id = @Id
END;

-- Get Email Template By Id
CREATE PROCEDURE GetEmailTemplateById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailTemplates WHERE Id = @Id
END;

-- Get Email Templates By Organization
CREATE PROCEDURE GetEmailTemplatesByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailTemplates 
    WHERE OrganizationId = @OrganizationId
    ORDER BY Type, Name
END;

-- Get Email Templates By Type
CREATE PROCEDURE GetEmailTemplatesByType
    @OrganizationId NVARCHAR(50),
    @Type NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailTemplates 
    WHERE OrganizationId = @OrganizationId AND Type = @Type
    ORDER BY Name
END;

-- Get Default Email Template By Type
CREATE PROCEDURE GetDefaultEmailTemplateByType
    @OrganizationId NVARCHAR(50),
    @Type NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailTemplates 
    WHERE OrganizationId = @OrganizationId AND Type = @Type AND IsDefault = 1
END;

-- Update Email Template
CREATE PROCEDURE UpdateEmailTemplate
    @Id NVARCHAR(50),
    @Name NVARCHAR(100),
    @Description NVARCHAR(500),
    @Subject NVARCHAR(255),
    @BodyContent NVARCHAR(MAX),
    @Type NVARCHAR(50),
    @IsDefault BIT,
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    -- If this is set as default, unset any existing default for this type
    IF @IsDefault = 1
    BEGIN
        UPDATE EmailTemplates
        SET IsDefault = 0
        WHERE OrganizationId = (SELECT OrganizationId FROM EmailTemplates WHERE Id = @Id)
          AND Type = @Type
          AND Id != @Id
    END

    UPDATE EmailTemplates
    SET 
        Name = @Name,
        Description = @Description,
        Subject = @Subject,
        BodyContent = @BodyContent,
        Type = @Type,
        IsDefault = @IsDefault,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM EmailTemplates WHERE Id = @Id
END;

-- Delete Email Template
CREATE PROCEDURE DeleteEmailTemplate
    @Id NVARCHAR(50)
AS
BEGIN
    DELETE FROM EmailTemplates WHERE Id = @Id
END;

-- Create Email Configuration
CREATE PROCEDURE CreateEmailConfiguration
    @Id NVARCHAR(50),
    @OrganizationId NVARCHAR(50),
    @SmtpServer NVARCHAR(255),
    @Port INT,
    @Username NVARCHAR(255),
    @Password NVARCHAR(255),
    @EnableSsl BIT,
    @IsDefault BIT,
    @SenderName NVARCHAR(100),
    @SenderEmail NVARCHAR(255),
    @CreatedBy NVARCHAR(50)
AS
BEGIN
    -- If this is set as default, unset any existing default
    IF @IsDefault = 1
    BEGIN
        UPDATE EmailConfigurations
        SET IsDefault = 0
        WHERE OrganizationId = @OrganizationId
    END

    INSERT INTO EmailConfigurations (
        Id, OrganizationId, SmtpServer, Port, Username, 
        Password, EnableSsl, IsDefault, SenderName, SenderEmail, CreatedBy
    )
    VALUES (
        @Id, @OrganizationId, @SmtpServer, @Port, @Username, 
        @Password, @EnableSsl, @IsDefault, @SenderName, @SenderEmail, @CreatedBy
    )
    
    SELECT * FROM EmailConfigurations WHERE Id = @Id
END;

-- Get Email Configuration By Id
CREATE PROCEDURE GetEmailConfigurationById
    @Id NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailConfigurations WHERE Id = @Id
END;

-- Get Email Configurations By Organization
CREATE PROCEDURE GetEmailConfigurationsByOrganization
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailConfigurations 
    WHERE OrganizationId = @OrganizationId
    ORDER BY IsDefault DESC, SenderName
END;

-- Get Default Email Configuration
CREATE PROCEDURE GetDefaultEmailConfiguration
    @OrganizationId NVARCHAR(50)
AS
BEGIN
    SELECT * FROM EmailConfigurations 
    WHERE OrganizationId = @OrganizationId AND IsDefault = 1
END;

-- Update Email Configuration
CREATE PROCEDURE UpdateEmailConfiguration
    @Id NVARCHAR(50),
    @SmtpServer NVARCHAR(255),
    @Port INT,
    @Username NVARCHAR(255),
    @Password NVARCHAR(255),
    @EnableSsl BIT,
    @IsDefault BIT,
    @SenderName NVARCHAR(100),
    @SenderEmail NVARCHAR(255),
    @UpdatedBy NVARCHAR(50)
AS
BEGIN
    -- If this is set as default, unset any existing default
    IF @IsDefault = 1
    BEGIN
        UPDATE EmailConfigurations
        SET IsDefault = 0
        WHERE OrganizationId = (SELECT OrganizationId FROM EmailConfigurations WHERE Id = @Id)
          AND Id != @Id
    END

    UPDATE EmailConfigurations
    SET 
        SmtpServer = @SmtpServer,
        Port = @Port,
        Username = @Username,
        Password = @Password,
        EnableSsl = @EnableSsl,
        IsDefault = @IsDefault,
        SenderName = @SenderName,
        SenderEmail = @SenderEmail,
        UpdatedAt = GETDATE(),
        UpdatedBy = @UpdatedBy
    WHERE Id = @Id
    
    SELECT * FROM EmailConfigurations WHERE Id = @Id
END;

-- Delete Email Configuration
CREATE PROCEDURE DeleteEmailConfiguration
    @Id NVARCHAR(50)
AS
BEGIN
    DELETE FROM EmailConfigurations WHERE Id = @Id
END;

-- ===== DEFAULT EMAIL TEMPLATES FOR PAYMENT SCHEDULES =====

-- Insert default email templates for payment schedule notifications
INSERT INTO EmailTemplates (Id, OrganizationId, Type, Name, Description, Subject, BodyContent, IsDefault, CreatedAt, CreatedBy)
VALUES
    (NEWID(), NULL, 'PAYMENT_SCHEDULE_CREATED', 'Payment Schedule Created', 'Default template for payment schedule creation notifications',
     'New Payment Schedule Created - {{PaymentProfileName}}',
     'Dear Valued Member,<br><br>A new payment schedule has been created for you:<br><br><strong>Payment Profile:</strong> {{PaymentProfileName}}<br><strong>Amount:</strong> {{Amount}}<br><strong>Due Date:</strong> {{DueDate}}<br><br>Please ensure payment is made by the due date to avoid any inconvenience.<br><br>Best regards,<br>Finance Team',
     1, GETDATE(), 'SYSTEM'),

    (NEWID(), NULL, 'PAYMENT_SCHEDULE_UPDATED', 'Payment Schedule Updated', 'Default template for payment schedule update notifications',
     'Payment Schedule Updated - {{PaymentProfileName}}',
     'Dear Valued Member,<br><br>Your payment schedule has been updated:<br><br><strong>Payment Profile:</strong> {{PaymentProfileName}}<br><strong>Amount:</strong> {{Amount}}<br><strong>Due Date:</strong> {{DueDate}}<br><strong>Reason:</strong> {{Reason}}<br><br>Please review the updated details and ensure payment is made by the new due date.<br><br>Best regards,<br>Finance Team',
     1, GETDATE(), 'SYSTEM'),

    (NEWID(), NULL, 'PAYMENT_SCHEDULE_DELETED', 'Payment Schedule Cancelled', 'Default template for payment schedule cancellation notifications',
     'Payment Schedule Cancelled - {{PaymentProfileName}}',
     'Dear Valued Member,<br><br>Your payment schedule has been cancelled:<br><br><strong>Payment Profile:</strong> {{PaymentProfileName}}<br><strong>Amount:</strong> {{Amount}}<br><strong>Original Due Date:</strong> {{DueDate}}<br><strong>Reason:</strong> {{Reason}}<br><br>If you have any questions, please contact our finance team.<br><br>Best regards,<br>Finance Team',
     1, GETDATE(), 'SYSTEM'),

    (NEWID(), NULL, 'BULK_SCHEDULES_IMPORTED', 'Bulk Payment Schedules Imported', 'Default template for bulk payment schedule import notifications',
     'Payment Schedules Imported - {{PaymentProfileName}}',
     'Dear Valued Member,<br><br>Payment schedules have been imported for {{PaymentProfileName}}:<br><br><strong>Successfully Imported:</strong> {{SuccessCount}} of {{TotalCount}} schedules<br><br>Please check your payment schedule to review the details and due dates.<br><br>Best regards,<br>Finance Team',
     1, GETDATE(), 'SYSTEM');