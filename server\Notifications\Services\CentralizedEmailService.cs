using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Mail;
using System.Threading.Tasks;
using Final_E_Receipt.Notifications.Models;
using Microsoft.Extensions.Logging;

namespace Final_E_Receipt.Notifications.Services
{
    /// <summary>
    /// Centralized email service that handles all email operations in the system
    /// Consolidates email sending, template processing, and configuration management
    /// </summary>
    public class CentralizedEmailService
    {
        private readonly EmailConfigurationService _emailConfigService;
        private readonly EmailTemplateService _emailTemplateService;
        private readonly UserEmailService _userEmailService;
        private readonly ILogger<CentralizedEmailService> _logger;

        public CentralizedEmailService(
            EmailConfigurationService emailConfigService,
            EmailTemplateService emailTemplateService,
            UserEmailService userEmailService,
            ILogger<CentralizedEmailService> logger)
        {
            _emailConfigService = emailConfigService;
            _emailTemplateService = emailTemplateService;
            _userEmailService = userEmailService;
            _logger = logger;
        }

        #region Core Email Sending Methods

        /// <summary>
        /// Send email using custom subject and body (no template)
        /// </summary>
        public async Task<bool> SendCustomEmailAsync(
            string organizationId,
            string recipientEmail,
            string recipientName,
            string subject,
            string body,
            bool isHtml = true)
        {
            try
            {
                var emailConfig = await GetEmailConfigurationAsync(organizationId);
                if (emailConfig == null) return false;

                return await SendEmailInternalAsync(
                    emailConfig,
                    recipientEmail,
                    recipientName,
                    subject,
                    body,
                    isHtml);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending custom email to {RecipientEmail}", recipientEmail);
                return false;
            }
        }

        /// <summary>
        /// Send email using template with placeholder replacement
        /// </summary>
        public async Task<bool> SendTemplatedEmailAsync(
            string organizationId,
            string templateType,
            string recipientEmail,
            string recipientName,
            Dictionary<string, string> templateData)
        {
            try
            {
                var emailConfig = await GetEmailConfigurationAsync(organizationId);
                if (emailConfig == null) return false;

                var emailTemplate = await GetEmailTemplateAsync(organizationId, templateType);
                if (emailTemplate == null) return false;

                var (subject, body) = ProcessTemplate(emailTemplate, templateData);

                return await SendEmailInternalAsync(
                    emailConfig,
                    recipientEmail,
                    recipientName,
                    subject,
                    body,
                    true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending templated email to {RecipientEmail} with template {TemplateType}", 
                    recipientEmail, templateType);
                return false;
            }
        }

        /// <summary>
        /// Send bulk emails to multiple recipients
        /// </summary>
        public async Task<BulkEmailResult> SendBulkEmailsAsync(
            string organizationId,
            List<EmailRecipient> recipients,
            string subject,
            string body,
            bool isHtml = true)
        {
            try
            {
                var emailConfig = await GetEmailConfigurationAsync(organizationId);
                if (emailConfig == null) 
                    return new BulkEmailResult { TotalEmails = recipients.Count, SuccessfulEmails = 0 };

                var tasks = new List<Task<bool>>();
                foreach (var recipient in recipients)
                {
                    tasks.Add(SendEmailInternalAsync(
                        emailConfig,
                        recipient.Email,
                        recipient.Name,
                        subject,
                        body,
                        isHtml));
                }

                var results = await Task.WhenAll(tasks);
                var successCount = 0;
                foreach (var result in results)
                {
                    if (result) successCount++;
                }

                _logger.LogInformation("Sent {SuccessfulEmails} of {TotalEmails} bulk emails", 
                    successCount, recipients.Count);

                return new BulkEmailResult 
                { 
                    TotalEmails = recipients.Count, 
                    SuccessfulEmails = successCount 
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending bulk emails");
                return new BulkEmailResult { TotalEmails = recipients.Count, SuccessfulEmails = 0 };
            }
        }

        #endregion

        #region Specialized Email Methods

        /// <summary>
        /// Send user invitation email
        /// </summary>
        public async Task<bool> SendUserInvitationAsync(
            string organizationId,
            string recipientEmail,
            string role,
            DateTime expiryDate,
            string loginUrl)
        {
            var templateData = new Dictionary<string, string>
            {
                { "RecipientEmail", recipientEmail },
                { "Role", GetRoleDisplayName(role) },
                { "ExpiryDate", expiryDate.ToString("MMMM dd, yyyy") },
                { "LoginUrl", loginUrl }
            };

            return await SendTemplatedEmailAsync(
                organizationId,
                "USER_INVITATION",
                recipientEmail,
                recipientEmail,
                templateData);
        }

        /// <summary>
        /// Send bulk payment schedules imported notification
        /// </summary>
        public async Task<bool> SendBulkSchedulesImportedNotificationAsync(
            string organizationId,
            string paymentProfileName,
            int successCount,
            int totalCount,
            List<EmailRecipient> recipients)
        {
            try
            {
                var subject = $"Payment Schedules Imported - {paymentProfileName}";
                var body = CreateBulkImportEmailBody(paymentProfileName, successCount, totalCount);

                var result = await SendBulkEmailsAsync(
                    organizationId,
                    recipients,
                    subject,
                    body,
                    true);

                _logger.LogInformation("Sent {SuccessfulEmails} of {TotalEmails} bulk import emails",
                    result.SuccessfulEmails, result.TotalEmails);

                return result.SuccessfulEmails > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending bulk schedules imported notifications");
                return false;
            }
        }

        /// <summary>
        /// Send bulk payment schedules imported notification using user IDs
        /// </summary>
        public async Task<bool> SendBulkSchedulesImportedNotificationAsync(
            string organizationId,
            string paymentProfileName,
            int successCount,
            int totalCount,
            string[] userIds)
        {
            try
            {
                var userEmails = await _userEmailService.GetUserEmails(userIds);
                var recipients = new List<EmailRecipient>();

                foreach (var userEmail in userEmails)
                {
                    recipients.Add(new EmailRecipient
                    {
                        Email = userEmail.Value,
                        Name = userEmail.Key
                    });
                }

                return await SendBulkSchedulesImportedNotificationAsync(
                    organizationId,
                    paymentProfileName,
                    successCount,
                    totalCount,
                    recipients);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending bulk schedules imported notifications with user IDs");
                return false;
            }
        }

        /// <summary>
        /// Send payment schedule notification
        /// </summary>
        public async Task<bool> SendPaymentScheduleEmailAsync(
            string organizationId,
            string templateType,
            string recipientEmail,
            string recipientName,
            string paymentProfileName,
            decimal amount,
            DateTime dueDate,
            string currency = "NGN")
        {
            var templateData = new Dictionary<string, string>
            {
                { "RecipientName", recipientName },
                { "PaymentProfileName", paymentProfileName },
                { "Amount", amount.ToString("N2") },
                { "Currency", currency },
                { "DueDate", dueDate.ToString("yyyy-MM-dd") }
            };

            return await SendTemplatedEmailAsync(
                organizationId,
                templateType,
                recipientEmail,
                recipientName,
                templateData);
        }

        /// <summary>
        /// Send payment schedule created notification using user ID
        /// </summary>
        public async Task<bool> SendPaymentScheduleCreatedNotificationAsync(
            string organizationId,
            string payerUserId,
            string paymentProfileName,
            decimal amount,
            DateTime dueDate,
            string currency = "NGN")
        {
            try
            {
                var userInfo = await _userEmailService.GetUserEmailInfo(payerUserId);
                if (userInfo?.Email == null)
                {
                    _logger.LogWarning("No email found for user {UserId}", payerUserId);
                    return false;
                }

                return await SendPaymentScheduleEmailAsync(
                    organizationId,
                    "PAYMENT_SCHEDULE_CREATED",
                    userInfo.Email,
                    userInfo.FullName,
                    paymentProfileName,
                    amount,
                    dueDate,
                    currency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending payment schedule created notification for user {UserId}", payerUserId);
                return false;
            }
        }

        /// <summary>
        /// Send payment schedule updated notification using user ID
        /// </summary>
        public async Task<bool> SendPaymentScheduleUpdatedNotificationAsync(
            string organizationId,
            string payerUserId,
            string paymentProfileName,
            decimal amount,
            DateTime dueDate,
            string currency = "NGN")
        {
            try
            {
                var userInfo = await _userEmailService.GetUserEmailInfo(payerUserId);
                if (userInfo?.Email == null)
                {
                    _logger.LogWarning("No email found for user {UserId}", payerUserId);
                    return false;
                }

                return await SendPaymentScheduleEmailAsync(
                    organizationId,
                    "PAYMENT_SCHEDULE_UPDATED",
                    userInfo.Email,
                    userInfo.FullName,
                    paymentProfileName,
                    amount,
                    dueDate,
                    currency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending payment schedule updated notification for user {UserId}", payerUserId);
                return false;
            }
        }

        /// <summary>
        /// Send payment schedule deleted notification using user ID
        /// </summary>
        public async Task<bool> SendPaymentScheduleDeletedNotificationAsync(
            string organizationId,
            string payerUserId,
            string paymentProfileName,
            decimal amount,
            DateTime dueDate,
            string currency = "NGN")
        {
            try
            {
                var userInfo = await _userEmailService.GetUserEmailInfo(payerUserId);
                if (userInfo?.Email == null)
                {
                    _logger.LogWarning("No email found for user {UserId}", payerUserId);
                    return false;
                }

                return await SendPaymentScheduleEmailAsync(
                    organizationId,
                    "PAYMENT_SCHEDULE_DELETED",
                    userInfo.Email,
                    userInfo.FullName,
                    paymentProfileName,
                    amount,
                    dueDate,
                    currency);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending payment schedule deleted notification for user {UserId}", payerUserId);
                return false;
            }
        }

        /// <summary>
        /// Send receipt notification
        /// </summary>
        public async Task<bool> SendReceiptEmailAsync(
            string organizationId,
            string recipientEmail,
            string recipientName,
            string receiptNumber,
            decimal amount,
            string currency,
            DateTime paymentDate,
            string paymentMethod,
            string description)
        {
            var templateData = new Dictionary<string, string>
            {
                { "RecipientName", recipientName },
                { "ReceiptNumber", receiptNumber },
                { "Amount", amount.ToString("N2") },
                { "Currency", currency },
                { "PaymentDate", paymentDate.ToString("yyyy-MM-dd") },
                { "PaymentMethod", paymentMethod },
                { "Description", description }
            };

            return await SendTemplatedEmailAsync(
                organizationId,
                "RECEIPT_NOTIFICATION",
                recipientEmail,
                recipientName,
                templateData);
        }

        /// <summary>
        /// Send compliance certificate issued notification
        /// </summary>
        public async Task<bool> SendCertificateIssuedNotificationAsync(
            string organizationId,
            string recipientEmail,
            string recipientName,
            string certificateNumber,
            string certificateType)
        {
            var templateData = new Dictionary<string, string>
            {
                { "RecipientName", recipientName },
                { "CertificateNumber", certificateNumber },
                { "CertificateType", certificateType }
            };

            return await SendTemplatedEmailAsync(
                organizationId,
                "CERTIFICATE_ISSUED",
                recipientEmail,
                recipientName,
                templateData);
        }

        /// <summary>
        /// Send compliance certificate expiry reminder
        /// </summary>
        public async Task<bool> SendCertificateExpiryReminderAsync(
            string organizationId,
            string recipientEmail,
            string recipientName,
            string certificateNumber,
            string certificateType,
            DateTime expiryDate)
        {
            var templateData = new Dictionary<string, string>
            {
                { "RecipientName", recipientName },
                { "CertificateNumber", certificateNumber },
                { "CertificateType", certificateType },
                { "ExpiryDate", expiryDate.ToString("yyyy-MM-dd") }
            };

            return await SendTemplatedEmailAsync(
                organizationId,
                "CERTIFICATE_EXPIRY_REMINDER",
                recipientEmail,
                recipientName,
                templateData);
        }

        /// <summary>
        /// Send compliance certificate revocation notification
        /// </summary>
        public async Task<bool> SendCertificateRevokedNotificationAsync(
            string organizationId,
            string recipientEmail,
            string recipientName,
            string certificateNumber,
            string certificateType,
            string revokedReason = null)
        {
            var templateData = new Dictionary<string, string>
            {
                { "RecipientName", recipientName },
                { "CertificateNumber", certificateNumber },
                { "CertificateType", certificateType },
                { "RevokedReason", revokedReason ?? "Not specified" }
            };

            return await SendTemplatedEmailAsync(
                organizationId,
                "CERTIFICATE_REVOKED",
                recipientEmail,
                recipientName,
                templateData);
        }

        #endregion

        #region Helper Methods

        private async Task<EmailConfiguration> GetEmailConfigurationAsync(string organizationId)
        {
            var emailConfig = await _emailConfigService.GetDefaultEmailConfiguration(organizationId);
            if (emailConfig == null)
            {
                _logger.LogError("No default email configuration found for organization {OrganizationId}", organizationId);
            }
            return emailConfig;
        }

        private async Task<EmailTemplate> GetEmailTemplateAsync(string organizationId, string templateType)
        {
            var emailTemplate = await _emailTemplateService.GetDefaultEmailTemplate(organizationId, templateType);
            if (emailTemplate == null)
            {
                _logger.LogError("No default email template found for organization {OrganizationId} and type {TemplateType}", 
                    organizationId, templateType);
            }
            return emailTemplate;
        }

        private (string subject, string body) ProcessTemplate(EmailTemplate template, Dictionary<string, string> templateData)
        {
            var subject = template.Subject;
            var body = template.BodyContent;

            foreach (var item in templateData)
            {
                var placeholder = $"{{{{{item.Key}}}}}";
                subject = subject.Replace(placeholder, item.Value ?? string.Empty);
                body = body.Replace(placeholder, item.Value ?? string.Empty);
            }

            return (subject, body);
        }

        private string GetRoleDisplayName(string role)
        {
            return role switch
            {
                "ADMIN" or "JTB_ADMIN" => "Administrator",
                "FINANCE_OFFICER" => "Finance Officer",
                "SENIOR_FINANCE_OFFICER" => "Senior Finance Officer",
                "PAYER" => "Payer",
                _ => role
            };
        }

        private string CreateBulkImportEmailBody(string paymentProfileName, int successCount, int totalCount)
        {
            return $@"
                <html>
                <body style='font-family: Arial, sans-serif; line-height: 1.6; color: #333;'>
                    <div style='max-width: 600px; margin: 0 auto; padding: 20px;'>
                        <h2 style='color: #045024;'>Payment Schedules Import Complete</h2>
                        <p>The bulk import for <strong>{paymentProfileName}</strong> has been completed.</p>
                        <p><strong>Import Summary:</strong></p>
                        <ul>
                            <li>Successfully imported: {successCount} schedules</li>
                            <li>Total processed: {totalCount} schedules</li>
                        </ul>
                        <p>Please check your payment schedule dashboard for details.</p>
                        <p>Best regards,<br>Payment Management System</p>
                    </div>
                </body>
                </html>";
        }

        private async Task<bool> SendEmailInternalAsync(
            EmailConfiguration config,
            string toEmail,
            string toName,
            string subject,
            string body,
            bool isHtml)
        {
            try
            {
                var message = new MailMessage
                {
                    From = new MailAddress(config.SenderEmail, config.SenderName),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = isHtml
                };

                message.To.Add(new MailAddress(toEmail, toName));

                using (var client = new SmtpClient(config.SmtpServer, config.Port))
                {
                    client.EnableSsl = config.EnableSsl;
                    client.Credentials = new NetworkCredential(config.Username, config.Password);

                    await client.SendMailAsync(message);
                    _logger.LogInformation("Email sent successfully to {ToEmail}", toEmail);
                    return true;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending email to {ToEmail}", toEmail);
                return false;
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Represents an email recipient
    /// </summary>
    public class EmailRecipient
    {
        public string Email { get; set; }
        public string Name { get; set; }
    }

    /// <summary>
    /// Result of bulk email operation
    /// </summary>
    public class BulkEmailResult
    {
        public int TotalEmails { get; set; }
        public int SuccessfulEmails { get; set; }
        public int FailedEmails => TotalEmails - SuccessfulEmails;
        public double SuccessRate => TotalEmails > 0 ? (double)SuccessfulEmails / TotalEmails * 100 : 0;
    }

    #endregion
}
