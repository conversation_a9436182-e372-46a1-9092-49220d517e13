using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Organizations.Models;
using Final_E_Receipt.Organizations.Services;
using Final_E_Receipt.Organizations.DTOs;
using System.Security.Claims;

namespace Final_E_Receipt.Organizations.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Policy = "RequireJTBAdmin")]
    public class OrganizationController : ControllerBase
    {
        private readonly OrganizationService _organizationService;

        public OrganizationController(OrganizationService organizationService)
        {
            _organizationService = organizationService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllOrganizations()
        {
            var organizations = await _organizationService.GetAllOrganizations();
            return Ok(organizations);
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> GetOrganizationById(string id)
        {
            var organization = await _organizationService.GetOrganizationById(id);
            
            if (organization == null)
                return NotFound(new { message = "Organization not found" });
                
            return Ok(organization);
        }

        [HttpPost]
        //[Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> CreateOrganization([FromBody] CreateOrganizationDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var organization = new Organization
            {
                Name = dto.Name,
                Email = dto.Email,
                PhoneNumber = dto.PhoneNumber,
                Address = dto.Address,
                City = dto.City,
                State = dto.State,
                Country = dto.Country,
                LogoUrl = dto.LogoUrl,
                Website = dto.Website,
                CreatedBy = userId
            };
            
            var createdOrganization = await _organizationService.CreateOrganization(organization);
            return CreatedAtAction(nameof(GetOrganizationById), new { id = createdOrganization.Id }, createdOrganization);
        }

        [HttpPut("{id}")]
        //[Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> UpdateOrganization(string id, [FromBody] UpdateOrganizationDTO dto)
        {
            var existingOrganization = await _organizationService.GetOrganizationById(id);
            
            if (existingOrganization == null)
                return NotFound(new { message = "Organization not found" });
                
            existingOrganization.Name = dto.Name;
            existingOrganization.Email = dto.Email;
            existingOrganization.PhoneNumber = dto.PhoneNumber;
            existingOrganization.Address = dto.Address;
            existingOrganization.City = dto.City;
            existingOrganization.State = dto.State;
            existingOrganization.Country = dto.Country;
            existingOrganization.LogoUrl = dto.LogoUrl;
            existingOrganization.Website = dto.Website;
            
            var updatedOrganization = await _organizationService.UpdateOrganization(existingOrganization);
            return Ok(updatedOrganization);
        }

        [HttpPatch("{id}/status")]
        //[Authorize(Roles = "JTB_ADMIN")]
        public async Task<IActionResult> ToggleOrganizationStatus(string id)
        {
            var organization = await _organizationService.GetOrganizationById(id);

            if (organization == null)
                return NotFound(new { message = "Organization not found" });

            organization.IsActive = !organization.IsActive;

            var updatedOrganization = await _organizationService.UpdateOrganization(organization);
            return Ok(updatedOrganization);
        }

        [HttpGet("search")]
        public async Task<IActionResult> SearchOrganizations([FromQuery] string query)
        {
            if (string.IsNullOrWhiteSpace(query))
            {
                return BadRequest(new { message = "Search query is required" });
            }

            var organizations = await _organizationService.SearchOrganizations(query);
            return Ok(organizations);
        }
    }
}