using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Final_E_Receipt.Payments.Services;
using Final_E_Receipt.Payments.DTOs;
using System.Security.Claims;

namespace Final_E_Receipt.Payments.Controllers
{
    [ApiController]
    [Route("api/payment-approval")]
    [Authorize]
    public class PaymentApprovalController : ControllerBase
    {
        private readonly PaymentApprovalService _approvalService;
        private readonly ILogger<PaymentApprovalController> _logger;

        public PaymentApprovalController(PaymentApprovalService approvalService, ILogger<PaymentApprovalController> logger)
        {
            _approvalService = approvalService;
            _logger = logger;
        }

        /// <summary>
        /// Finance Officer acknowledges a payment
        /// </summary>
        [HttpPost("{paymentId}/acknowledge")]
        [Authorize(Roles = "FINANCE_OFFICER")]
        public async Task<IActionResult> AcknowledgePayment(string paymentId, [FromBody] AcknowledgePaymentDTO dto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized(new { message = "User not found" });
                }

                var payment = await _approvalService.AcknowledgePayment(paymentId, currentUserId, dto.Notes);
                
                if (payment == null)
                {
                    return NotFound(new { message = "Payment not found or not in valid status for acknowledgment" });
                }

                return Ok(new PaymentDTO
                {
                    Id = payment.Id,
                    PayerId = payment.PayerId,
                    PayerName = payment.PayerName,
                    PayerEmail = payment.PayerEmail,
                    Amount = payment.Amount,
                    Currency = payment.Currency,
                    TransactionReference = payment.TransactionReference,
                    PaymentMethod = payment.PaymentMethod,
                    Status = payment.Status,
                    Description = payment.Description,
                    Category = payment.Category,
                    CreatedAt = payment.CreatedAt,
                    CompletedAt = payment.CompletedAt,
                    ReceiptId = payment.ReceiptId,
                    OrganizationId = payment.OrganizationId,
                    ProofFileId = payment.ProofFileId,
                    AcknowledgedBy = payment.AcknowledgedBy,
                    AcknowledgedDate = payment.AcknowledgedDate,
                    AcknowledgedNotes = payment.AcknowledgedNotes,
                    ApprovedBy = payment.ApprovedBy,
                    ApprovedDate = payment.ApprovedDate,
                    ApprovalNotes = payment.ApprovalNotes,
                    RejectedBy = payment.RejectedBy,
                    RejectedDate = payment.RejectedDate,
                    RejectionReason = payment.RejectionReason,
                    PaymentScheduleId = payment.PaymentScheduleId
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error acknowledging payment {PaymentId}", paymentId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Senior Finance Officer approves a payment
        /// </summary>
        [HttpPost("{paymentId}/approve")]
        [Authorize(Roles = "SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> ApprovePayment(string paymentId, [FromBody] ApprovePaymentDTO dto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized(new { message = "User not found" });
                }

                var payment = await _approvalService.ApprovePayment(paymentId, currentUserId, dto.Notes);
                
                if (payment == null)
                {
                    return NotFound(new { message = "Payment not found or not acknowledged yet" });
                }

                return Ok(MapToPaymentDTO(payment));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error approving payment {PaymentId}", paymentId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Reject a payment (Finance Officer or Senior Finance Officer)
        /// </summary>
        [HttpPost("{paymentId}/reject")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> RejectPayment(string paymentId, [FromBody] RejectPaymentDTO dto)
        {
            try
            {
                var currentUserId = GetCurrentUserId();
                if (string.IsNullOrEmpty(currentUserId))
                {
                    return Unauthorized(new { message = "User not found" });
                }

                var payment = await _approvalService.RejectPayment(paymentId, currentUserId, dto.Reason, dto.Notes);
                
                if (payment == null)
                {
                    return NotFound(new { message = "Payment not found or not in valid status for rejection" });
                }

                return Ok(MapToPaymentDTO(payment));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting payment {PaymentId}", paymentId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get payments pending acknowledgment (Finance Officers)
        /// </summary>
        [HttpGet("pending-acknowledgment")]
        [Authorize(Roles = "FINANCE_OFFICER,ADMIN")]
        public async Task<IActionResult> GetPaymentsPendingAcknowledgment([FromQuery] string organizationId = null)
        {
            try
            {
                // If user is not admin, filter by their organization
                if (!User.IsInRole("ADMIN"))
                {
                    organizationId = GetCurrentUserOrganizationId();
                }

                var payments = await _approvalService.GetPaymentsPendingAcknowledgment(organizationId);
                var paymentDtos = payments.Select(MapToPaymentDTO).ToList();

                return Ok(paymentDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments pending acknowledgment");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get payments pending approval (Senior Finance Officers)
        /// </summary>
        [HttpGet("pending-approval")]
        [Authorize(Roles = "SENIOR_FINANCE_OFFICER,ADMIN")]
        public async Task<IActionResult> GetPaymentsPendingApproval([FromQuery] string organizationId = null)
        {
            try
            {
                // If user is not admin, filter by their organization
                if (!User.IsInRole("ADMIN"))
                {
                    organizationId = GetCurrentUserOrganizationId();
                }

                var payments = await _approvalService.GetPaymentsPendingApproval(organizationId);
                var paymentDtos = payments.Select(MapToPaymentDTO).ToList();

                return Ok(paymentDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting payments pending approval");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get payment approval history
        /// </summary>
        [HttpGet("{paymentId}/history")]
        [Authorize(Roles = "FINANCE_OFFICER,SENIOR_FINANCE_OFFICER,ADMIN")]
        public async Task<IActionResult> GetPaymentApprovalHistory(string paymentId)
        {
            try
            {
                var payment = await _approvalService.GetPaymentApprovalHistory(paymentId);
                
                if (payment == null)
                {
                    return NotFound(new { message = "Payment not found" });
                }

                return Ok(MapToPaymentDTO(payment));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting approval history for payment {PaymentId}", paymentId);
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        /// <summary>
        /// Get current user ID from claims
        /// </summary>
        private string GetCurrentUserId()
        {
            return User.FindFirst("sub")?.Value ?? 
                   User.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value ??
                   User.FindFirst("oid")?.Value;
        }

        /// <summary>
        /// Get current user's organization ID from claims
        /// </summary>
        private string GetCurrentUserOrganizationId()
        {
            // This would need to be implemented based on how organization info is stored in claims
            // For now, return null to get all organizations
            return null;
        }

        /// <summary>
        /// Map Payment to PaymentDTO
        /// </summary>
        private PaymentDTO MapToPaymentDTO(Final_E_Receipt.Payments.Models.Payment payment)
        {
            return new PaymentDTO
            {
                Id = payment.Id,
                PayerId = payment.PayerId,
                PayerName = payment.PayerName,
                PayerEmail = payment.PayerEmail,
                Amount = payment.Amount,
                Currency = payment.Currency,
                TransactionReference = payment.TransactionReference,
                PaymentMethod = payment.PaymentMethod,
                Status = payment.Status,
                Description = payment.Description,
                Category = payment.Category,
                CreatedAt = payment.CreatedAt,
                CompletedAt = payment.CompletedAt,
                ReceiptId = payment.ReceiptId,
                OrganizationId = payment.OrganizationId,
                ProofFileId = payment.ProofFileId,
                AcknowledgedBy = payment.AcknowledgedBy,
                AcknowledgedDate = payment.AcknowledgedDate,
                AcknowledgedNotes = payment.AcknowledgedNotes,
                ApprovedBy = payment.ApprovedBy,
                ApprovedDate = payment.ApprovedDate,
                ApprovalNotes = payment.ApprovalNotes,
                RejectedBy = payment.RejectedBy,
                RejectedDate = payment.RejectedDate,
                RejectionReason = payment.RejectionReason,
                PaymentScheduleId = payment.PaymentScheduleId
            };
        }
    }
}
