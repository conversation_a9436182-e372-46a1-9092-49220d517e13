using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Final_E_Receipt.Payments.Models;
using Final_E_Receipt.Services;
using Final_E_Receipt.Receipts.Services;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Common.Services;
using Final_E_Receipt.Common.Models;

namespace Final_E_Receipt.Payments.Services
{
    public class PaymentService
    {
        private readonly IDatabaseService _dbService;
        private readonly ReceiptService _receiptService;
        private readonly PaymentApprovalService _approvalService;
        private PaymentComplianceService _complianceService;
        private readonly PaymentTypeService _paymentTypeService;
        private readonly AuditService _auditService;

        public PaymentService(IDatabaseService dbService, ReceiptService receiptService, PaymentApprovalService approvalService, PaymentTypeService paymentTypeService, AuditService auditService)
        {
            _dbService = dbService;
            _receiptService = receiptService;
            _approvalService = approvalService;
            _paymentTypeService = paymentTypeService;
            _auditService = auditService;
        }

        // Method to set compliance service (to avoid circular dependency)
        public void SetComplianceService(PaymentComplianceService complianceService)
        {
            _complianceService = complianceService;
        }

        public async Task<Payment> InitiatePayment(Payment payment)
        {
            // Validate payment type
            if (!await _paymentTypeService.ValidatePaymentType(payment.PaymentTypeId))
            {
                throw new InvalidOperationException("Invalid or inactive payment type");
            }

            payment.Id = Guid.NewGuid().ToString();
            payment.TransactionReference = GenerateTransactionReference();
            payment.Status = "Pending";
            payment.CreatedAt = DateTime.Now;
            var parameters = new
            {
                Id = payment.Id,
                PayerId = payment.PayerId,
                PayerName = payment.PayerName,
                PayerEmail = payment.PayerEmail,
                Amount = payment.Amount,
                Currency = payment.Currency,
                TransactionReference = payment.TransactionReference,
                PaymentMethod = payment.PaymentMethod,
                Status = payment.Status,
                Description = payment.Description,
                PaymentTypeId = payment.PaymentTypeId,
                OrganizationId = payment.OrganizationId
            };


            var createdPayment = await _dbService.QueryFirstOrDefaultAsync<Payment>("CreatePayment", parameters);

            // Log payment creation
            if (createdPayment != null)
            {
                await _auditService.LogAsync(
                    AuditActions.PAYMENT_CREATED,
                    EntityTypes.PAYMENT,
                    createdPayment.Id,
                    null,
                    new {
                        createdPayment.PayerId,
                        createdPayment.PayerName,
                        createdPayment.Amount,
                        createdPayment.Currency,
                        createdPayment.PaymentMethod,
                        createdPayment.PaymentTypeId,
                        createdPayment.OrganizationId
                    },
                    $"Payment initiated for {payment.Amount} {payment.Currency}"
                );
            }

            return createdPayment;
        }

        public async Task<Payment> CompletePayment(string paymentId)
        {
            var parameters = new
            {
                Id = paymentId,
                Status = "Completed",
                CompletedAt = DateTime.Now
            };

            var payment = await _dbService.QueryFirstOrDefaultAsync<Payment>("UpdatePaymentStatus", parameters);
            
            if (payment == null)
                return null;
            
            // Generate receipt for completed payment
            if (payment.Status == "Completed")
            {
                var receipt = new Receipt
                {
                    PayerId = payment.PayerId,
                    PayerName = payment.PayerName,
                    PayerEmail = payment.PayerEmail,
                    Amount = payment.Amount,
                    Currency = payment.Currency,
                    PaymentDate = payment.CompletedAt.Value,
                    PaymentMethod = payment.PaymentMethod,
                    Status = "Completed",
                    Description = payment.Description,
                    Category = payment.Category,
                    CreatedBy = payment.PayerId,
                    OrganizationId = payment.OrganizationId,
                    PaymentId = payment.Id // Link receipt to payment for file association
                };
                
                var createdReceipt = await _receiptService.CreateReceipt(receipt);
                payment.ReceiptId = createdReceipt.Id;
                
                // Update payment with receipt ID
                await _dbService.ExecuteAsync("UpdatePaymentReceiptId", new
                {
                    Id = payment.Id,
                    ReceiptId = payment.ReceiptId
                });

                // Check for compliance certificate eligibility
                if (_complianceService != null)
                {
                    try
                    {
                        await _complianceService.CheckAndCreateComplianceCertificate(payment);
                    }
                    catch (Exception ex)
                    {
                        // Log error but don't fail the payment completion
                        // This will be logged in the compliance service
                    }
                }
            }

            return payment;
        }

        public async Task<Payment> GetPaymentById(string id)
        {
            var parameters = new { Id = id };
            return await _dbService.QueryFirstOrDefaultAsync<Payment>("GetPaymentById", parameters);
        }

        public async Task<List<Payment>> GetPaymentsByPayer(string payerId)
        {
            var parameters = new { PayerId = payerId };
            var payments = await _dbService.QueryAsync<Payment>("GetPaymentsByPayer", parameters);
            return payments.ToList();
        }

        public async Task<List<Payment>> GetPaymentsByOrganization(string organizationId)
        {
            var parameters = new { OrganizationId = organizationId };
            var payments = await _dbService.QueryAsync<Payment>("GetPaymentsByOrganization", parameters);
            return payments.ToList();
        }

        public async Task<List<Payment>> GetPaymentsByStatus(string status)
        {
            var parameters = new { Status = status };
            var payments = await _dbService.QueryAsync<Payment>("GetPaymentsByStatus", parameters);
            return payments.ToList();
        }

        // ===== APPROVAL WORKFLOW INTEGRATION =====

        /// <summary>
        /// Acknowledge payment (Finance Officer) - delegates to PaymentApprovalService
        /// </summary>
        public async Task<Payment> AcknowledgePayment(string paymentId, string financeOfficerId, string notes = null)
        {
            return await _approvalService.AcknowledgePayment(paymentId, financeOfficerId, notes);
        }

        /// <summary>
        /// Approve payment (Senior Finance Officer) - delegates to PaymentApprovalService
        /// </summary>
        public async Task<Payment> ApprovePayment(string paymentId, string seniorFinanceOfficerId, string notes = null)
        {
            return await _approvalService.ApprovePayment(paymentId, seniorFinanceOfficerId, notes);
        }

        /// <summary>
        /// Reject payment - delegates to PaymentApprovalService
        /// </summary>
        public async Task<Payment> RejectPayment(string paymentId, string rejectedBy, string reason, string notes = null)
        {
            return await _approvalService.RejectPayment(paymentId, rejectedBy, reason, notes);
        }

        /// <summary>
        /// Get payments pending acknowledgment - delegates to PaymentApprovalService
        /// </summary>
        public async Task<List<Payment>> GetPaymentsPendingAcknowledgment(string organizationId = null)
        {
            return await _approvalService.GetPaymentsPendingAcknowledgment(organizationId);
        }

        /// <summary>
        /// Get payments pending approval - delegates to PaymentApprovalService
        /// </summary>
        public async Task<List<Payment>> GetPaymentsPendingApproval(string organizationId = null)
        {
            return await _approvalService.GetPaymentsPendingApproval(organizationId);
        }

        /// <summary>
        /// Get payment approval history - delegates to PaymentApprovalService
        /// </summary>
        public async Task<Payment> GetPaymentApprovalHistory(string paymentId)
        {
            return await _approvalService.GetPaymentApprovalHistory(paymentId);
        }

        private string GenerateTransactionReference()
        {
            // Format: TXN-YYYYMMDD-XXXXX
            var dateString = DateTime.Now.ToString("yyyyMMdd");
            var random = new Random();
            var randomPart = random.Next(10000, 99999).ToString();

            return $"TXN-{dateString}-{randomPart}";
        }
    }
}


