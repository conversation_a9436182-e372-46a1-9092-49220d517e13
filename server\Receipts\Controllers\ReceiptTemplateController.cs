using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Final_E_Receipt.Receipts.DTOs;
using Final_E_Receipt.Receipts.Models;
using Final_E_Receipt.Receipts.Services;
using System;
using System.Collections.Generic;
using System.Security.Claims;
using System.Threading.Tasks;

namespace Final_E_Receipt.Receipts.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class ReceiptTemplateController : ControllerBase
    {
        private readonly ReceiptTemplateService _templateService;

        public ReceiptTemplateController(ReceiptTemplateService templateService)
        {
            _templateService = templateService;
        }

        [HttpPost]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> CreateReceiptTemplate([FromBody] CreateReceiptTemplateDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var template = new ReceiptTemplate
            {
                Name = dto.Name,
                Description = dto.Description,
                TemplateContent = dto.TemplateContent,
                IsDefault = dto.IsDefault,
                OrganizationId = dto.OrganizationId,
                CreatedBy = userId
            };

            var createdTemplate = await _templateService.CreateReceiptTemplate(template);
            
            if (createdTemplate == null)
                return BadRequest(new { message = "Failed to create receipt template" });

            return Ok(createdTemplate);
        }

        [HttpGet("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetReceiptTemplateById(string id)
        {
            var template = await _templateService.GetReceiptTemplateById(id);
            
            if (template == null)
                return NotFound(new { message = "Receipt template not found" });

            return Ok(template);
        }

        [HttpGet("organization/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetReceiptTemplatesByOrganization(string organizationId)
        {
            var templates = await _templateService.GetReceiptTemplatesByOrganization(organizationId);
            return Ok(templates);
        }

        [HttpGet("default/{organizationId}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> GetDefaultReceiptTemplate(string organizationId)
        {
            var template = await _templateService.GetDefaultReceiptTemplate(organizationId);
            
            if (template == null)
                return NotFound(new { message = "Default receipt template not found" });

            return Ok(template);
        }

        [HttpPut("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> UpdateReceiptTemplate(string id, [FromBody] UpdateReceiptTemplateDTO dto)
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            
            var existingTemplate = await _templateService.GetReceiptTemplateById(id);
            
            if (existingTemplate == null)
                return NotFound(new { message = "Receipt template not found" });

            existingTemplate.Name = dto.Name;
            existingTemplate.Description = dto.Description;
            existingTemplate.TemplateContent = dto.TemplateContent;
            existingTemplate.IsDefault = dto.IsDefault;
            existingTemplate.UpdatedBy = userId;

            var updatedTemplate = await _templateService.UpdateReceiptTemplate(existingTemplate);
            
            if (updatedTemplate == null)
                return BadRequest(new { message = "Failed to update receipt template" });

            return Ok(updatedTemplate);
        }

        [HttpDelete("{id}")]
        [Authorize(Roles = "JTB_ADMIN,FINANCE_OFFICER,SENIOR_FINANCE_OFFICER")]
        public async Task<IActionResult> DeleteReceiptTemplate(string id)
        {
            var existingTemplate = await _templateService.GetReceiptTemplateById(id);
            
            if (existingTemplate == null)
                return NotFound(new { message = "Receipt template not found" });

            var result = await _templateService.DeleteReceiptTemplate(id);
            
            if (!result)
                return BadRequest(new { message = "Failed to delete receipt template" });

            return Ok(new { message = "Receipt template deleted successfully" });
        }
    }
}