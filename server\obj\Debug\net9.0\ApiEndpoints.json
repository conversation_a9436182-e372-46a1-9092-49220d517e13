[{"ContainingType": "Final_E_Receipt.Authentication.Controllers.AdminSetupController", "Method": "InitializeAdmin", "RelativePath": "api/AdminSetup/initialize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AdminSetupController", "Method": "RegisterAdmin", "RelativePath": "api/AdminSetup/register-admin", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Authentication.Controllers.AuthController+RegisterAdminDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Common.Controllers.AuditController", "Method": "GetAuditLogs", "RelativePath": "api/Audit", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "action", "Type": "System.String", "IsRequired": false}, {"Name": "entityType", "Type": "System.String", "IsRequired": false}, {"Name": "organizationId", "Type": "System.String", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Common.Controllers.AuditController", "Method": "GetEntityAuditLogs", "RelativePath": "api/Audit/entity/{entityType}/{entityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityType", "Type": "System.String", "IsRequired": true}, {"Name": "entityId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Common.Controllers.AuditController", "Method": "ExportAuditLogs", "RelativePath": "api/Audit/export", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "action", "Type": "System.String", "IsRequired": false}, {"Name": "entityType", "Type": "System.String", "IsRequired": false}, {"Name": "organizationId", "Type": "System.String", "IsRequired": false}, {"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Common.Controllers.AuditController", "Method": "GetAuditMetadata", "RelativePath": "api/Audit/metadata", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Common.Controllers.AuditController", "Method": "GetRecentAuditLogs", "RelativePath": "api/Audit/recent", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "count", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Common.Controllers.AuditController", "Method": "GetAuditStats", "RelativePath": "api/Audit/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthController", "Method": "DetectAuthMethod", "RelativePath": "api/auth/detect", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Authentication.Controllers.AuthController+EmailDetectionDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthController", "Method": "ExchangeMicrosoftToken", "RelativePath": "api/auth/exchange", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthController", "Method": "HealthCheck", "RelativePath": "api/auth/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthLocalController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/local/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Authentication.DTOs.LocalLoginDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthLocalController", "Method": "CompleteSetup", "RelativePath": "api/auth/local/setup", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Authentication.DTOs.LocalRegistrationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthController", "Method": "<PERSON><PERSON>", "RelativePath": "api/auth/login", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Authentication.Controllers.AuthController+LoginDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthController", "Method": "GetCurrentUser", "RelativePath": "api/auth/me", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.AuthController", "Method": "GetAuthStatus", "RelativePath": "api/auth/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Documents.Controllers.CertificateTemplateController", "Method": "GetTemplatePreview", "RelativePath": "api/certificate-templates/{templateId}/preview", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "templateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Documents.Controllers.CertificateTemplateController", "Method": "GetAvailableTemplates", "RelativePath": "api/certificate-templates/available", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": false}, {"Name": "certificateType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Documents.Controllers.CertificateTemplateController", "Method": "GetOrganizationTemplates", "RelativePath": "api/certificate-templates/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateFileController", "Method": "GetCertificateSupportingFiles", "RelativePath": "api/certificates/{certificateId}/files", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "certificateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateFileController", "Method": "DeleteSupportingDocument", "RelativePath": "api/certificates/{certificateId}/files/{fileId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "certificateId", "Type": "System.String", "IsRequired": true}, {"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateFileController", "Method": "GenerateAndStoreCertificatePdf", "RelativePath": "api/certificates/{certificateId}/files/generate-pdf", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "certificateId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Final_E_Receipt.Compliance.Controllers.GenerateCertificatePdfRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateFileController", "Method": "LinkPaymentProofsToCertificate", "RelativePath": "api/certificates/{certificateId}/files/link-payment-proofs", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "certificateId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Final_E_Receipt.Compliance.Controllers.LinkPaymentProofsRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateFileController", "Method": "GetCertificateFileStats", "RelativePath": "api/certificates/{certificateId}/files/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "certificateId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.ComplianceOverviewController", "Method": "GetComplianceAlerts", "RelativePath": "api/compliance/alerts", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.ComplianceOverviewController", "Method": "GetAllOrganizationsComplianceStatus", "RelativePath": "api/compliance/organizations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.ComplianceOverviewController", "Method": "UpdateOrganizationComplianceStatus", "RelativePath": "api/compliance/organizations/{organizationId}/update-status", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Final_E_Receipt.Organizations.Controllers.UpdateComplianceStatusRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "CreateComplianceCertificate", "RelativePath": "api/ComplianceCertificate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Compliance.DTOs.CreateComplianceCertificateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "GetComplianceCertificate", "RelativePath": "api/ComplianceCertificate/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "DownloadCertificatePdf", "RelativePath": "api/ComplianceCertificate/{id}/download-pdf", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "RevokeCertificate", "RelativePath": "api/ComplianceCertificate/{id}/revoke", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Compliance.DTOs.RevokeCertificateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "UpdateCertificateStatus", "RelativePath": "api/ComplianceCertificate/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Final_E_Receipt.Compliance.Controllers.UpdateCertificateStatusRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "GetCertificateWithFiles", "RelativePath": "api/ComplianceCertificate/{id}/with-files", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "BulkGenerateCertificates", "RelativePath": "api/ComplianceCertificate/bulk-generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Compliance.DTOs.BulkCertificateGenerationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "GetExpiringCertificates", "RelativePath": "api/ComplianceCertificate/expiring", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "daysFromNow", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "GetCertificatesByOrganization", "RelativePath": "api/ComplianceCertificate/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Compliance.Controllers.ComplianceCertificateController", "Method": "SearchCertificates", "RelativePath": "api/ComplianceCertificate/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "searchDto", "Type": "Final_E_Receipt.Compliance.DTOs.CertificateSearchDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "GetComplianceDashboard", "RelativePath": "api/ComplianceReporting/dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "GetExpiringCertificatesReport", "RelativePath": "api/ComplianceReporting/expiring-certificates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "daysFromNow", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "ExportDashboardReport", "RelativePath": "api/ComplianceReporting/export/dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "format", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "ExportExpiringCertificatesReport", "RelativePath": "api/ComplianceReporting/export/expiring-certificates", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "daysFromNow", "Type": "System.Int32", "IsRequired": false}, {"Name": "format", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "ExportOrganizationReport", "RelativePath": "api/ComplianceReporting/export/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "format", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "GetCertificateIssuanceStats", "RelativePath": "api/ComplianceReporting/issuance-stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "GetComplianceMetrics", "RelativePath": "api/ComplianceReporting/metrics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ComplianceReportingController", "Method": "GetOrganizationComplianceReport", "RelativePath": "api/ComplianceReporting/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailConfigurationController", "Method": "CreateEmailConfiguration", "RelativePath": "api/EmailConfiguration", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Notifications.DTOs.CreateEmailConfigurationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailConfigurationController", "Method": "GetEmailConfigurationById", "RelativePath": "api/EmailConfiguration/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailConfigurationController", "Method": "UpdateEmailConfiguration", "RelativePath": "api/EmailConfiguration/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Notifications.DTOs.UpdateEmailConfigurationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailConfigurationController", "Method": "DeleteEmailConfiguration", "RelativePath": "api/EmailConfiguration/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailConfigurationController", "Method": "GetDefaultEmailConfiguration", "RelativePath": "api/EmailConfiguration/default/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailConfigurationController", "Method": "GetEmailConfigurationsByOrganization", "RelativePath": "api/EmailConfiguration/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "CreateEmailTemplate", "RelativePath": "api/EmailTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Notifications.DTOs.CreateEmailTemplateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "GetEmailTemplateById", "RelativePath": "api/EmailTemplate/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "UpdateEmailTemplate", "RelativePath": "api/EmailTemplate/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Notifications.DTOs.UpdateEmailTemplateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "DeleteEmailTemplate", "RelativePath": "api/EmailTemplate/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "GetDefaultEmailTemplate", "RelativePath": "api/EmailTemplate/default/{organizationId}/{type}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "GetEmailTemplatesByOrganization", "RelativePath": "api/EmailTemplate/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.EmailTemplateController", "Method": "GetEmailTemplatesByType", "RelativePath": "api/EmailTemplate/type/{organizationId}/{type}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "type", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Files.Controllers.FileController", "Method": "GetFileById", "RelativePath": "api/File/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Files.Controllers.FileController", "Method": "DeleteFile", "RelativePath": "api/File/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Files.Controllers.FileController", "Method": "DownloadFile", "RelativePath": "api/File/download/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Files.Controllers.FileController", "Method": "GetFilesByEntity", "RelativePath": "api/File/entity/{entityType}/{entityId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityType", "Type": "System.String", "IsRequired": true}, {"Name": "entityId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Files.Controllers.FileController", "Method": "UploadFile", "RelativePath": "api/File/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "relatedEntityType", "Type": "System.String", "IsRequired": false}, {"Name": "relatedEntityId", "Type": "System.String", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}, {"Name": "category", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.NotificationController", "Method": "GetUserNotifications", "RelativePath": "api/Notification", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Type", "Type": "System.String", "IsRequired": false}, {"Name": "Priority", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.NotificationController", "Method": "DeleteNotification", "RelativePath": "api/Notification/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.NotificationController", "Method": "MarkAsRead", "RelativePath": "api/Notification/{id}/read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.NotificationController", "Method": "BulkAction", "RelativePath": "api/Notification/bulk-action", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Notifications.DTOs.NotificationBulkActionDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.NotificationController", "Method": "BulkMarkAsRead", "RelativePath": "api/Notification/bulk/read", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Notifications.DTOs.NotificationBulkMarkAsReadDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Notifications.Controllers.NotificationController", "Method": "GetNotificationStats", "RelativePath": "api/Notification/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationController", "Method": "GetAllOrganizations", "RelativePath": "api/Organization", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationController", "Method": "CreateOrganization", "RelativePath": "api/Organization", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Organizations.DTOs.CreateOrganizationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationController", "Method": "GetOrganizationById", "RelativePath": "api/Organization/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationController", "Method": "UpdateOrganization", "RelativePath": "api/Organization/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Organizations.DTOs.UpdateOrganizationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationController", "Method": "ToggleOrganizationStatus", "RelativePath": "api/Organization/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationController", "Method": "SearchOrganizations", "RelativePath": "api/Organization/search", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "query", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationComplianceController", "Method": "GetCertificateRequirements", "RelativePath": "api/organizations/{organizationId}/compliance/requirements", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationComplianceController", "Method": "GetOrganizationComplianceStatus", "RelativePath": "api/organizations/{organizationId}/compliance/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Organizations.Controllers.OrganizationComplianceController", "Method": "GetOrganizationComplianceSummary", "RelativePath": "api/organizations/{organizationId}/compliance/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentController", "Method": "InitiatePayment", "RelativePath": "api/Payment", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Payments.DTOs.CreatePaymentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentApprovalController", "Method": "AcknowledgePayment", "RelativePath": "api/payment-approval/{paymentId}/acknowledge", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Payments.DTOs.AcknowledgePaymentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentApprovalController", "Method": "ApprovePayment", "RelativePath": "api/payment-approval/{paymentId}/approve", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Payments.DTOs.ApprovePaymentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentApprovalController", "Method": "GetPaymentApprovalHistory", "RelativePath": "api/payment-approval/{paymentId}/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentApprovalController", "Method": "RejectPayment", "RelativePath": "api/payment-approval/{paymentId}/reject", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Payments.DTOs.RejectPaymentDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentApprovalController", "Method": "GetPaymentsPendingAcknowledgment", "RelativePath": "api/payment-approval/pending-acknowledgment", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentApprovalController", "Method": "GetPaymentsPendingApproval", "RelativePath": "api/payment-approval/pending-approval", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "Create", "RelativePath": "api/payment-types", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Payments.Controllers.PaymentTypeCreateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "GetAll", "RelativePath": "api/payment-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "isActive", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "GetById", "RelativePath": "api/payment-types/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "Update", "RelativePath": "api/payment-types/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Payments.Controllers.PaymentTypeUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "Delete", "RelativePath": "api/payment-types/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "UpdateStatus", "RelativePath": "api/payment-types/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Payments.Controllers.PaymentTypeStatusUpdateDto", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentTypeController", "Method": "GetSummary", "RelativePath": "api/payment-types/summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "startDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "endDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "organizationId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentController", "Method": "GetPaymentById", "RelativePath": "api/Payment/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentController", "Method": "CompletePayment", "RelativePath": "api/Payment/{id}/complete", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentController", "Method": "UpdatePaymentStatus", "RelativePath": "api/Payment/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Payments.DTOs.UpdatePaymentStatusDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentController", "Method": "GetPaymentsByPayer", "RelativePath": "api/Payment/payer/{payerId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "payerId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProfileController", "Method": "CreatePaymentProfile", "RelativePath": "api/PaymentProfile", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "profile", "Type": "Final_E_Receipt.Payments.Models.PaymentProfile", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProfileController", "Method": "GetPaymentProfileById", "RelativePath": "api/PaymentProfile/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProfileController", "Method": "UpdatePaymentProfile", "RelativePath": "api/PaymentProfile/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "profile", "Type": "Final_E_Receipt.Payments.Models.PaymentProfile", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProfileController", "Method": "DeletePaymentProfile", "RelativePath": "api/PaymentProfile/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProfileController", "Method": "BulkCreatePaymentSchedules", "RelativePath": "api/PaymentProfile/{profileId}/schedules", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "profileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProfileController", "Method": "GetPaymentProfilesByOrganization", "RelativePath": "api/PaymentProfile/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProofController", "Method": "GetPaymentProofFiles", "RelativePath": "api/payments/{paymentId}/proof", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProofController", "Method": "DeletePaymentProof", "RelativePath": "api/payments/{paymentId}/proof/{fileId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProofController", "Method": "DownloadPaymentProof", "RelativePath": "api/payments/{paymentId}/proof/{fileId}/download", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "fileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProofController", "Method": "ValidatePaymentProof", "RelativePath": "api/payments/{paymentId}/proof/{fileId}/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "fileId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Final_E_Receipt.Payments.Controllers.ValidateProofRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentProofController", "Method": "UploadPaymentProof", "RelativePath": "api/payments/{paymentId}/proof/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentId", "Type": "System.String", "IsRequired": true}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "CreatePaymentSchedule", "RelativePath": "api/PaymentSchedule", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "schedule", "Type": "Final_E_Receipt.Payments.Models.PaymentSchedule", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "GetPaymentScheduleById", "RelativePath": "api/PaymentSchedule/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "DeletePaymentSchedule", "RelativePath": "api/PaymentSchedule/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "UpdatePaymentScheduleStatus", "RelativePath": "api/PaymentSchedule/{id}/status", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "Final_E_Receipt.Payments.Controllers.UpdateScheduleStatusRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "ImportFromExcel", "RelativePath": "api/PaymentSchedule/import/{paymentProfileId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "paymentProfileId", "Type": "System.String", "IsRequired": true}, {"Name": "excelFile", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "GetPaymentSchedulesByOrganization", "RelativePath": "api/PaymentSchedule/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "GetPaymentSchedulesByProfile", "RelativePath": "api/PaymentSchedule/profile/{profileId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "profileId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Payments.Controllers.PaymentScheduleController", "Method": "DownloadExcelTemplate", "RelativePath": "api/PaymentSchedule/template", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "CreateReceipt", "RelativePath": "api/Receipt", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Receipts.DTOs.CreateReceiptDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "GetReceiptById", "RelativePath": "api/Receipt/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "MarkNotificationSent", "RelativePath": "api/Receipt/{id}/notification", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "RevokeReceipt", "RelativePath": "api/Receipt/{id}/revoke", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Receipts.DTOs.RevokeReceiptDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "GetReceiptsByOrganization", "RelativePath": "api/Receipt/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "GetReceiptsByDateRange", "RelativePath": "api/Receipt/organization/{organizationId}/daterange", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "startDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "endDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "GetReceiptsByPayer", "RelativePath": "api/Receipt/payer/{payerId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "payerId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptController", "Method": "SearchReceipts", "RelativePath": "api/Receipt/search", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": false}, {"Name": "dto", "Type": "Final_E_Receipt.Receipts.DTOs.SearchReceiptsDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptFileController", "Method": "GetReceiptSupportingFiles", "RelativePath": "api/receipts/{receiptId}/files", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "receiptId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptFileController", "Method": "AttachFileToReceipt", "RelativePath": "api/receipts/{receiptId}/files/attach", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "receiptId", "Type": "System.String", "IsRequired": true}, {"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptFileController", "Method": "GetReceiptFileStats", "RelativePath": "api/receipts/{receiptId}/files/stats", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "receiptId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptWithFilesController", "Method": "GetReceiptWithFiles", "RelativePath": "api/receipts/{receiptId}/with-files", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "receiptId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptWithFilesController", "Method": "CreateReceiptWithFiles", "RelativePath": "api/receipts/with-files", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Receipts.Controllers.CreateReceiptWithFilesDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptTemplateController", "Method": "CreateReceiptTemplate", "RelativePath": "api/ReceiptTemplate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Receipts.DTOs.CreateReceiptTemplateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptTemplateController", "Method": "GetReceiptTemplateById", "RelativePath": "api/ReceiptTemplate/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptTemplateController", "Method": "UpdateReceiptTemplate", "RelativePath": "api/ReceiptTemplate/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Receipts.DTOs.UpdateReceiptTemplateDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptTemplateController", "Method": "DeleteReceiptTemplate", "RelativePath": "api/ReceiptTemplate/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptTemplateController", "Method": "GetDefaultReceiptTemplate", "RelativePath": "api/ReceiptTemplate/default/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Receipts.Controllers.ReceiptTemplateController", "Method": "GetReceiptTemplatesByOrganization", "RelativePath": "api/ReceiptTemplate/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetCategorySummary", "RelativePath": "api/Reporting/category-summary/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetDailyRevenue", "RelativePath": "api/Reporting/daily-revenue/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "StartDate", "Type": "System.DateTime", "IsRequired": false}, {"Name": "EndDate", "Type": "System.DateTime", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetDashboardSummary", "RelativePath": "api/Reporting/dashboard-summary/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "ExportReport", "RelativePath": "api/Reporting/export/{organizationId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Reporting.DTOs.ExportReportDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetMonthlyRevenue", "RelativePath": "api/Reporting/monthly-revenue/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "Year", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetOutstandingBalances", "RelativePath": "api/Reporting/outstanding-balances", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Final_E_Receipt.Reporting.Models.OutstandingBalancesFilter", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "ExportOutstandingBalances", "RelativePath": "api/Reporting/outstanding-balances/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Final_E_Receipt.Reporting.Controllers.OutstandingBalancesExportRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetPaymentHistory", "RelativePath": "api/Reporting/payment-history", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Final_E_Receipt.Reporting.Models.PaymentHistoryFilter", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "ExportPaymentHistory", "RelativePath": "api/Reporting/payment-history/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Final_E_Receipt.Reporting.Controllers.PaymentHistoryExportRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetPaymentMethodSummary", "RelativePath": "api/Reporting/payment-method-summary/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetRevokedReceipts", "RelativePath": "api/Reporting/revoked-receipts", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "filter", "Type": "Final_E_Receipt.Reporting.Models.RevokedReceiptsFilter", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "ExportRevokedReceipts", "RelativePath": "api/Reporting/revoked-receipts/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "Final_E_Receipt.Reporting.Controllers.RevokedReceiptsExportRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetTopPayers", "RelativePath": "api/Reporting/top-payers/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "Limit", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Reporting.Controllers.ReportingController", "Method": "GetYearOverYearComparison", "RelativePath": "api/Reporting/year-over-year/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}, {"Name": "CurrentYear", "Type": "System.Int32", "IsRequired": false}, {"Name": "PreviousYear", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserController", "Method": "GetAllUsers", "RelativePath": "api/User", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserInvitationController", "Method": "GetAllInvitations", "RelativePath": "api/user-invitations", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserInvitationController", "Method": "CancelInvitation", "RelativePath": "api/user-invitations/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserInvitationController", "Method": "ResendInvitation", "RelativePath": "api/user-invitations/{id}/resend", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserInvitationController", "Method": "InviteUser", "RelativePath": "api/user-invitations/invite", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "dto", "Type": "Final_E_Receipt.Authentication.DTOs.CreateInvitationDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserInvitationController", "Method": "GetPendingInvitations", "RelativePath": "api/user-invitations/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserController", "Method": "GetUserById", "RelativePath": "api/User/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserController", "Method": "UpdateUser", "RelativePath": "api/User/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Authentication.DTOs.UpdateUserDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserController", "Method": "PatchUser", "RelativePath": "api/User/{id}", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Authentication.DTOs.UpdateUserDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserController", "Method": "UpdateUserStatus", "RelativePath": "api/User/{id}/status", "HttpMethod": "PATCH", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.String", "IsRequired": true}, {"Name": "dto", "Type": "Final_E_Receipt.Authentication.DTOs.UpdateUserStatusDTO", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Authentication.Controllers.UserController", "Method": "GetUsersByOrganization", "RelativePath": "api/User/organization/{organizationId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "organizationId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "Final_E_Receipt.Controllers.WeatherForecastController", "Method": "Get", "RelativePath": "WeatherForecast", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[Final_E_Receipt.WeatherForecast, Final_E_Receipt, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}], "EndpointName": "GetWeatherForecast"}]