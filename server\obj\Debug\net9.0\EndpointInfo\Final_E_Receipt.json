{"openapi": "3.0.1", "info": {"title": "Final E-Receipt API", "description": "A comprehensive API for electronic receipt management", "contact": {"name": "Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/AdminSetup/initialize": {"post": {"tags": ["AdminSetup"], "responses": {"200": {"description": "OK"}}}}, "/api/AdminSetup/register-admin": {"post": {"tags": ["AdminSetup"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterAdminDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterAdminDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterAdminDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Audit": {"get": {"tags": ["Audit"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}, {"name": "action", "in": "query", "schema": {"type": "string"}}, {"name": "entityType", "in": "query", "schema": {"type": "string"}}, {"name": "organizationId", "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}], "responses": {"200": {"description": "OK"}}}}, "/api/Audit/entity/{entityType}/{entityId}": {"get": {"tags": ["Audit"], "parameters": [{"name": "entityType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Audit/recent": {"get": {"tags": ["Audit"], "parameters": [{"name": "count", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 20}}], "responses": {"200": {"description": "OK"}}}}, "/api/Audit/stats": {"get": {"tags": ["Audit"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Audit/metadata": {"get": {"tags": ["Audit"], "responses": {"200": {"description": "OK"}}}}, "/api/Audit/export": {"get": {"tags": ["Audit"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}, {"name": "action", "in": "query", "schema": {"type": "string"}}, {"name": "entityType", "in": "query", "schema": {"type": "string"}}, {"name": "organizationId", "in": "query", "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/status": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/health": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/detect": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmailDetectionDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/EmailDetectionDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/EmailDetectionDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/exchange": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK"}}}}, "/api/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/local/setup": {"post": {"tags": ["AuthLocal"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalRegistrationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocalRegistrationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LocalRegistrationDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/auth/local/login": {"post": {"tags": ["AuthLocal"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocalLoginDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LocalLoginDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LocalLoginDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/certificate-templates/available": {"get": {"tags": ["CertificateTemplate"], "parameters": [{"name": "organizationId", "in": "query", "schema": {"type": "string"}}, {"name": "certificateType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/certificate-templates/{templateId}/preview": {"get": {"tags": ["CertificateTemplate"], "parameters": [{"name": "templateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/certificate-templates/organization/{organizationId}": {"get": {"tags": ["CertificateTemplate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate": {"post": {"tags": ["ComplianceCertificate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateComplianceCertificateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateComplianceCertificateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateComplianceCertificateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/{id}": {"get": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/{id}/with-files": {"get": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/organization/{organizationId}": {"get": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/search": {"post": {"tags": ["ComplianceCertificate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CertificateSearchDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CertificateSearchDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CertificateSearchDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/{id}/status": {"put": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCertificateStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateCertificateStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateCertificateStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/{id}/revoke": {"post": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokeCertificateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokeCertificateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokeCertificateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/expiring": {"get": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "daysFromNow", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 30}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/{id}/download-pdf": {"get": {"tags": ["ComplianceCertificate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceCertificate/bulk-generate": {"post": {"tags": ["ComplianceCertificate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkCertificateGenerationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkCertificateGenerationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkCertificateGenerationDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/certificates/{certificateId}/files": {"get": {"tags": ["ComplianceCertificateFile"], "parameters": [{"name": "certificateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/certificates/{certificateId}/files/{fileId}": {"delete": {"tags": ["ComplianceCertificateFile"], "parameters": [{"name": "certificateId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/certificates/{certificateId}/files/stats": {"get": {"tags": ["ComplianceCertificateFile"], "parameters": [{"name": "certificateId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/certificates/{certificateId}/files/generate-pdf": {"post": {"tags": ["ComplianceCertificateFile"], "parameters": [{"name": "certificateId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/GenerateCertificatePdfRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/GenerateCertificatePdfRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/GenerateCertificatePdfRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/certificates/{certificateId}/files/link-payment-proofs": {"post": {"tags": ["ComplianceCertificateFile"], "parameters": [{"name": "certificateId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LinkPaymentProofsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LinkPaymentProofsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LinkPaymentProofsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/compliance/organizations": {"get": {"tags": ["ComplianceOverview"], "responses": {"200": {"description": "OK"}}}}, "/api/compliance/alerts": {"get": {"tags": ["ComplianceOverview"], "responses": {"200": {"description": "OK"}}}}, "/api/compliance/organizations/{organizationId}/update-status": {"post": {"tags": ["ComplianceOverview"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateComplianceStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateComplianceStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateComplianceStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/dashboard": {"get": {"tags": ["ComplianceReporting"], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/organization/{organizationId}": {"get": {"tags": ["ComplianceReporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/metrics": {"get": {"tags": ["ComplianceReporting"], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/issuance-stats": {"get": {"tags": ["ComplianceReporting"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/expiring-certificates": {"get": {"tags": ["ComplianceReporting"], "parameters": [{"name": "daysFromNow", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 30}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/export/dashboard": {"get": {"tags": ["ComplianceReporting"], "parameters": [{"name": "format", "in": "query", "schema": {"type": "string", "default": "pdf"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/export/organization/{organizationId}": {"get": {"tags": ["ComplianceReporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "format", "in": "query", "schema": {"type": "string", "default": "pdf"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ComplianceReporting/export/expiring-certificates": {"get": {"tags": ["ComplianceReporting"], "parameters": [{"name": "daysFromNow", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 30}}, {"name": "format", "in": "query", "schema": {"type": "string", "default": "excel"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailConfiguration": {"post": {"tags": ["EmailConfiguration"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmailConfigurationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateEmailConfigurationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateEmailConfigurationDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EmailConfiguration/organization/{organizationId}": {"get": {"tags": ["EmailConfiguration"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailConfiguration/{id}": {"get": {"tags": ["EmailConfiguration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["EmailConfiguration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEmailConfigurationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateEmailConfigurationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateEmailConfigurationDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["EmailConfiguration"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailConfiguration/default/{organizationId}": {"get": {"tags": ["EmailConfiguration"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplate": {"post": {"tags": ["EmailTemplate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateEmailTemplateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateEmailTemplateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateEmailTemplateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplate/organization/{organizationId}": {"get": {"tags": ["EmailTemplate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplate/type/{organizationId}/{type}": {"get": {"tags": ["EmailTemplate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplate/{id}": {"get": {"tags": ["EmailTemplate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["EmailTemplate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEmailTemplateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateEmailTemplateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateEmailTemplateDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["EmailTemplate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/EmailTemplate/default/{organizationId}/{type}": {"get": {"tags": ["EmailTemplate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/File/upload": {"post": {"tags": ["File"], "parameters": [{"name": "relatedEntityType", "in": "query", "schema": {"type": "string"}}, {"name": "relatedEntityId", "in": "query", "schema": {"type": "string"}}, {"name": "description", "in": "query", "schema": {"type": "string"}}, {"name": "category", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/File/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/File/download/{id}": {"get": {"tags": ["File"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/File/entity/{entityType}/{entityId}": {"get": {"tags": ["File"], "parameters": [{"name": "entityType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Notification": {"get": {"tags": ["Notification"], "parameters": [{"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Type", "in": "query", "schema": {"type": "string"}}, {"name": "Priority", "in": "query", "schema": {"type": "string"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Notification/stats": {"get": {"tags": ["Notification"], "responses": {"200": {"description": "OK"}}}}, "/api/Notification/{id}/read": {"post": {"tags": ["Notification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Notification/bulk/read": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationBulkMarkAsReadDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationBulkMarkAsReadDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationBulkMarkAsReadDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Notification/bulk-action": {"post": {"tags": ["Notification"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotificationBulkActionDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/NotificationBulkActionDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/NotificationBulkActionDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Notification/{id}": {"delete": {"tags": ["Notification"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Organization": {"get": {"tags": ["Organization"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Organization"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateOrganizationDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Organization/{id}": {"get": {"tags": ["Organization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Organization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateOrganizationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateOrganizationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateOrganizationDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Organization/{id}/status": {"patch": {"tags": ["Organization"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Organization/search": {"get": {"tags": ["Organization"], "parameters": [{"name": "query", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/organizations/{organizationId}/compliance/status": {"get": {"tags": ["OrganizationCompliance"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/organizations/{organizationId}/compliance/summary": {"get": {"tags": ["OrganizationCompliance"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/organizations/{organizationId}/compliance/requirements": {"get": {"tags": ["OrganizationCompliance"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Payment": {"post": {"tags": ["Payment"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePaymentDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Payment/{id}": {"get": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Payment/payer/{payerId}": {"get": {"tags": ["Payment"], "parameters": [{"name": "payerId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Payment/{id}/complete": {"put": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Payment/{id}/status": {"put": {"tags": ["Payment"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePaymentStatusDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/payment-approval/{paymentId}/acknowledge": {"post": {"tags": ["PaymentApproval"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AcknowledgePaymentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AcknowledgePaymentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AcknowledgePaymentDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/payment-approval/{paymentId}/approve": {"post": {"tags": ["PaymentApproval"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ApprovePaymentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApprovePaymentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApprovePaymentDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/payment-approval/{paymentId}/reject": {"post": {"tags": ["PaymentApproval"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectPaymentDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RejectPaymentDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RejectPaymentDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/payment-approval/pending-acknowledgment": {"get": {"tags": ["PaymentApproval"], "parameters": [{"name": "organizationId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payment-approval/pending-approval": {"get": {"tags": ["PaymentApproval"], "parameters": [{"name": "organizationId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payment-approval/{paymentId}/history": {"get": {"tags": ["PaymentApproval"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentProfile": {"post": {"tags": ["PaymentProfile"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentProfile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentProfile"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentProfile"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PaymentProfile/{id}": {"get": {"tags": ["PaymentProfile"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["PaymentProfile"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentProfile"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentProfile"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentProfile"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["PaymentProfile"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentProfile/organization/{organizationId}": {"get": {"tags": ["PaymentProfile"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentProfile/{profileId}/schedules": {"post": {"tags": ["PaymentProfile"], "parameters": [{"name": "profileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payments/{paymentId}/proof/upload": {"post": {"tags": ["PaymentProof"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "description", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/payments/{paymentId}/proof": {"get": {"tags": ["PaymentProof"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payments/{paymentId}/proof/{fileId}/download": {"get": {"tags": ["PaymentProof"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payments/{paymentId}/proof/{fileId}": {"delete": {"tags": ["PaymentProof"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payments/{paymentId}/proof/{fileId}/validate": {"post": {"tags": ["PaymentProof"], "parameters": [{"name": "paymentId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "fileId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidateProofRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ValidateProofRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ValidateProofRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule": {"post": {"tags": ["PaymentSchedule"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentSchedule"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentSchedule"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentSchedule"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule/{id}": {"get": {"tags": ["PaymentSchedule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["PaymentSchedule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule/organization/{organizationId}": {"get": {"tags": ["PaymentSchedule"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule/profile/{profileId}": {"get": {"tags": ["PaymentSchedule"], "parameters": [{"name": "profileId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule/{id}/status": {"put": {"tags": ["PaymentSchedule"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateScheduleStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule/import/{paymentProfileId}": {"post": {"tags": ["PaymentSchedule"], "parameters": [{"name": "paymentProfileId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"excelFile": {"type": "string", "format": "binary"}}}, "encoding": {"excelFile": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/PaymentSchedule/template": {"get": {"tags": ["PaymentSchedule"], "responses": {"200": {"description": "OK"}}}}, "/api/payment-types": {"post": {"tags": ["PaymentType"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentTypeCreateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTypeCreateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentTypeCreateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["PaymentType"], "parameters": [{"name": "isActive", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payment-types/{id}": {"get": {"tags": ["PaymentType"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["PaymentType"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentTypeUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTypeUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentTypeUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["PaymentType"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/payment-types/{id}/status": {"patch": {"tags": ["PaymentType"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentTypeStatusUpdateDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentTypeStatusUpdateDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentTypeStatusUpdateDto"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/payment-types/summary": {"get": {"tags": ["PaymentType"], "parameters": [{"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "organizationId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receipt": {"post": {"tags": ["Receipt"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceiptDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/{id}": {"get": {"tags": ["Receipt"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/payer/{payerId}": {"get": {"tags": ["Receipt"], "parameters": [{"name": "payerId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/organization/{organizationId}": {"get": {"tags": ["Receipt"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/organization/{organizationId}/daterange": {"get": {"tags": ["Receipt"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "endDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/{id}/revoke": {"put": {"tags": ["Receipt"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokeReceiptDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokeReceiptDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokeReceiptDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/{id}/notification": {"put": {"tags": ["Receipt"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Receipt/search": {"post": {"tags": ["Receipt"], "parameters": [{"name": "organizationId", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchReceiptsDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchReceiptsDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchReceiptsDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/receipts/{receiptId}/files": {"get": {"tags": ["ReceiptFile"], "parameters": [{"name": "receiptId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/receipts/{receiptId}/files/attach": {"post": {"tags": ["ReceiptFile"], "parameters": [{"name": "receiptId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "description", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/receipts/{receiptId}/files/stats": {"get": {"tags": ["ReceiptFile"], "parameters": [{"name": "receiptId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ReceiptTemplate": {"post": {"tags": ["ReceiptTemplate"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptTemplateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptTemplateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceiptTemplateDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/ReceiptTemplate/{id}": {"get": {"tags": ["ReceiptTemplate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["ReceiptTemplate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReceiptTemplateDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReceiptTemplateDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReceiptTemplateDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["ReceiptTemplate"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ReceiptTemplate/organization/{organizationId}": {"get": {"tags": ["ReceiptTemplate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/ReceiptTemplate/default/{organizationId}": {"get": {"tags": ["ReceiptTemplate"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/receipts/{receiptId}/with-files": {"get": {"tags": ["ReceiptWithFiles"], "parameters": [{"name": "receiptId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/receipts/with-files": {"post": {"tags": ["ReceiptWithFiles"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptWithFilesDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateReceiptWithFilesDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateReceiptWithFilesDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/dashboard-summary/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/monthly-revenue/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Year", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/payment-method-summary/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/category-summary/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/daily-revenue/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/top-payers/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "Limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/year-over-year/{organizationId}": {"get": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "CurrentYear", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PreviousYear", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/export/{organizationId}": {"post": {"tags": ["Reporting"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportReportDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportReportDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportReportDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/payment-history": {"post": {"tags": ["Reporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentHistoryFilter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentHistoryFilter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentHistoryFilter"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/outstanding-balances": {"post": {"tags": ["Reporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OutstandingBalancesFilter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OutstandingBalancesFilter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OutstandingBalancesFilter"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/revoked-receipts": {"post": {"tags": ["Reporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokedReceiptsFilter"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokedReceiptsFilter"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokedReceiptsFilter"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/payment-history/export": {"post": {"tags": ["Reporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentHistoryExportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentHistoryExportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentHistoryExportRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/outstanding-balances/export": {"post": {"tags": ["Reporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OutstandingBalancesExportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OutstandingBalancesExportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OutstandingBalancesExportRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reporting/revoked-receipts/export": {"post": {"tags": ["Reporting"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RevokedReceiptsExportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RevokedReceiptsExportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RevokedReceiptsExportRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User": {"get": {"tags": ["User"], "responses": {"200": {"description": "OK"}}}}, "/api/User/{id}": {"get": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}}}, "responses": {"200": {"description": "OK"}}}, "patch": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/{id}/status": {"patch": {"tags": ["User"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserStatusDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/User/organization/{organizationId}": {"get": {"tags": ["User"], "parameters": [{"name": "organizationId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/user-invitations/invite": {"post": {"tags": ["UserInvitation"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateInvitationDTO"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateInvitationDTO"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateInvitationDTO"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/user-invitations/pending": {"get": {"tags": ["UserInvitation"], "responses": {"200": {"description": "OK"}}}}, "/api/user-invitations": {"get": {"tags": ["UserInvitation"], "responses": {"200": {"description": "OK"}}}}, "/api/user-invitations/{id}/resend": {"post": {"tags": ["UserInvitation"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/user-invitations/{id}": {"delete": {"tags": ["UserInvitation"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/WeatherForecast": {"get": {"tags": ["WeatherForecast"], "operationId": "GetWeatherForecast", "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WeatherForecast"}}}}}}}}}, "components": {"schemas": {"AcknowledgePaymentDTO": {"type": "object", "properties": {"notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ApprovePaymentDTO": {"type": "object", "properties": {"notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthenticationType": {"enum": [0, 1], "type": "integer", "format": "int32"}, "BulkCertificateGenerationDTO": {"type": "object", "properties": {"paymentProfileId": {"type": "string", "nullable": true}, "certificateType": {"type": "string", "nullable": true}, "complianceYear": {"type": "string", "nullable": true}, "compliancePeriod": {"type": "string", "nullable": true}, "validFrom": {"type": "string", "format": "date-time"}, "validUntil": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "terms": {"type": "string", "nullable": true}, "regulatoryBody": {"type": "string", "nullable": true}, "licenseCategory": {"type": "string", "nullable": true}, "organizationIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "generatePdfImmediately": {"type": "boolean"}}, "additionalProperties": false}, "CertificateSearchDTO": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "certificateType": {"type": "string", "nullable": true}, "status": {"type": "string", "nullable": true}, "complianceYear": {"type": "string", "nullable": true}, "compliancePeriod": {"type": "string", "nullable": true}, "validFromStart": {"type": "string", "format": "date-time", "nullable": true}, "validFromEnd": {"type": "string", "format": "date-time", "nullable": true}, "validUntilStart": {"type": "string", "format": "date-time", "nullable": true}, "validUntilEnd": {"type": "string", "format": "date-time", "nullable": true}, "isRevoked": {"type": "boolean", "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CreateComplianceCertificateDTO": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "paymentProfileId": {"type": "string", "nullable": true}, "certificateType": {"type": "string", "nullable": true}, "totalAmount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "validFrom": {"type": "string", "format": "date-time"}, "validUntil": {"type": "string", "format": "date-time"}, "description": {"type": "string", "nullable": true}, "terms": {"type": "string", "nullable": true}, "complianceYear": {"type": "string", "nullable": true}, "compliancePeriod": {"type": "string", "nullable": true}, "regulatoryBody": {"type": "string", "nullable": true}, "licenseCategory": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "selectedTemplateId": {"type": "string", "nullable": true}, "preferredOrientation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateEmailConfigurationDTO": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "smtpServer": {"type": "string", "nullable": true}, "port": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "enableSsl": {"type": "boolean"}, "isDefault": {"type": "boolean"}, "senderName": {"type": "string", "nullable": true}, "senderEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateEmailTemplateDTO": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "bodyContent": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}}, "additionalProperties": false}, "CreateInvitationDTO": {"required": ["authType", "email", "organizationId", "role"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "role": {"minLength": 1, "type": "string"}, "organizationId": {"minLength": 1, "type": "string"}, "authType": {"$ref": "#/components/schemas/AuthenticationType"}}, "additionalProperties": false}, "CreateOrganizationDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreatePaymentDTO": {"type": "object", "properties": {"payerId": {"type": "string", "nullable": true}, "payerName": {"type": "string", "nullable": true}, "payerEmail": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "paymentMethod": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "paymentTypeId": {"type": "string", "nullable": true}, "organizationId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateReceiptDTO": {"type": "object", "properties": {"payerId": {"type": "string", "nullable": true}, "payerName": {"type": "string", "nullable": true}, "payerEmail": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "paymentDate": {"type": "string", "format": "date-time"}, "paymentMethod": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "organizationId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateReceiptTemplateDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "templateContent": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "organizationId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateReceiptWithFilesDTO": {"type": "object", "properties": {"payerId": {"type": "string", "nullable": true}, "payerName": {"type": "string", "nullable": true}, "payerEmail": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "paymentDate": {"type": "string", "format": "date-time"}, "paymentMethod": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "organizationId": {"type": "string", "nullable": true}, "paymentId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "EmailDetectionDTO": {"type": "object", "properties": {"email": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExportReportDTO": {"type": "object", "properties": {"reportType": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "year": {"type": "integer", "format": "int32", "nullable": true}, "exportFormat": {"type": "string", "nullable": true}}, "additionalProperties": false}, "GenerateCertificatePdfRequest": {"type": "object", "properties": {"templateId": {"type": "string", "nullable": true}, "customFields": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LinkPaymentProofsRequest": {"type": "object", "properties": {"paymentIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LocalLoginDTO": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LocalRegistrationDTO": {"required": ["email", "firstName", "lastName", "newPassword", "temporaryPassword"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "temporaryPassword": {"minLength": 1, "type": "string"}, "newPassword": {"minLength": 8, "type": "string"}, "firstName": {"minLength": 1, "type": "string"}, "lastName": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "NotificationBulkActionDTO": {"required": ["action", "notificationIds"], "type": "object", "properties": {"notificationIds": {"type": "array", "items": {"type": "string"}}, "action": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "NotificationBulkMarkAsReadDTO": {"required": ["notificationIds"], "type": "object", "properties": {"notificationIds": {"type": "array", "items": {"type": "string"}}}, "additionalProperties": false}, "OutstandingBalancesExportRequest": {"type": "object", "properties": {"filter": {"$ref": "#/components/schemas/OutstandingBalancesFilter"}, "format": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OutstandingBalancesFilter": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "includeOverdue": {"type": "boolean"}, "minAmount": {"type": "number", "format": "double", "nullable": true}, "agingDays": {"type": "integer", "format": "int32"}, "paymentProfileId": {"type": "string", "nullable": true}, "sortBy": {"type": "string", "nullable": true}, "sortDirection": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentHistoryExportRequest": {"type": "object", "properties": {"filter": {"$ref": "#/components/schemas/PaymentHistoryFilter"}, "format": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentHistoryFilter": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "paymentStatus": {"type": "string", "nullable": true}, "paymentMethod": {"type": "string", "nullable": true}, "minAmount": {"type": "number", "format": "double", "nullable": true}, "maxAmount": {"type": "number", "format": "double", "nullable": true}, "payerName": {"type": "string", "nullable": true}, "payerEmail": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sortBy": {"type": "string", "nullable": true}, "sortDirection": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentProfile": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "frequency": {"type": "string", "nullable": true}, "dueDay": {"type": "integer", "format": "int32"}, "isFixedAmount": {"type": "boolean"}, "isActive": {"type": "boolean"}, "paymentTypeId": {"type": "string", "nullable": true}, "organizationId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentSchedule": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "paymentProfileId": {"type": "string", "nullable": true}, "organizationId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "dueDate": {"type": "string", "format": "date-time"}, "status": {"type": "string", "nullable": true}, "paymentId": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "string", "nullable": true}, "profileName": {"type": "string", "nullable": true}, "organizationName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentTypeCreateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentTypeStatusUpdateDto": {"type": "object", "properties": {"isActive": {"type": "boolean"}}, "additionalProperties": false}, "PaymentTypeUpdateDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterAdminDto": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RejectPaymentDTO": {"type": "object", "properties": {"reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RevokeCertificateDTO": {"type": "object", "properties": {"reason": {"type": "string", "nullable": true}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RevokeReceiptDTO": {"type": "object", "properties": {"revokedReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RevokedReceiptsExportRequest": {"type": "object", "properties": {"filter": {"$ref": "#/components/schemas/RevokedReceiptsFilter"}, "format": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RevokedReceiptsFilter": {"type": "object", "properties": {"organizationId": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "revokedBy": {"type": "string", "nullable": true}, "reasonCategory": {"type": "string", "nullable": true}, "payerName": {"type": "string", "nullable": true}, "minAmount": {"type": "number", "format": "double", "nullable": true}, "maxAmount": {"type": "number", "format": "double", "nullable": true}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sortBy": {"type": "string", "nullable": true}, "sortDirection": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SearchReceiptsDTO": {"type": "object", "properties": {"searchTerm": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UpdateCertificateStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateComplianceStatusRequest": {"type": "object", "properties": {"certificateId": {"type": "string", "nullable": true}, "action": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateEmailConfigurationDTO": {"type": "object", "properties": {"smtpServer": {"type": "string", "nullable": true}, "port": {"type": "integer", "format": "int32"}, "username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "enableSsl": {"type": "boolean"}, "isDefault": {"type": "boolean"}, "senderName": {"type": "string", "nullable": true}, "senderEmail": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateEmailTemplateDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "bodyContent": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}}, "additionalProperties": false}, "UpdateOrganizationDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "address": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "logoUrl": {"type": "string", "nullable": true}, "website": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePaymentStatusDTO": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateReceiptTemplateDTO": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "templateContent": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}}, "additionalProperties": false}, "UpdateScheduleStatusRequest": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "paymentId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserDTO": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "organizationId": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateUserStatusDTO": {"type": "object", "properties": {"isActive": {"type": "boolean"}}, "additionalProperties": false}, "ValidateProofRequest": {"type": "object", "properties": {"isValid": {"type": "boolean"}, "comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "WeatherForecast": {"type": "object", "properties": {"date": {"type": "string", "format": "date"}, "temperatureC": {"type": "integer", "format": "int32"}, "temperatureF": {"type": "integer", "format": "int32", "readOnly": true}, "summary": {"type": "string", "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "http", "description": "JWT Authorization header using the Bear<PERSON> scheme.\r\n                      Enter 'Bearer' [space] and then your token.\r\n                      Example: 'Bearer 12345abcdef'", "scheme": "bearer", "bearerFormat": "JWT"}}}, "security": [{"Bearer": []}]}